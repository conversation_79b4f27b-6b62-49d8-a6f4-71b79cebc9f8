# Generated by Django 3.2.2 on 2023-07-03 10:02

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('user_authentication', '0015_customuser_is_bbi_user'),
    ]

    operations = [
        migrations.AddField(
            model_name='userreportdetails',
            name='published_at',
            field=models.DateTimeField(default=None, null=True),
        ),
        migrations.AlterUniqueTogether(
            name='userreportdetails',
            unique_together={('dataset', 'report_name')},
        ),
        migrations.AddField(
            model_name='userreportdetails',
            name='report_year',
            field=models.Char<PERSON>ield(default=None, max_length=20),
        ),
    ]
