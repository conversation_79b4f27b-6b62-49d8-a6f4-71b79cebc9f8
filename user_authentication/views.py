import os
import time
import calendar
import csv
from json import loads
from collections import defaultdict
from operator import itemgetter
import itertools
from datetime import date, datetime, timedelta
from dateutil.relativedelta import relativedelta
from django.utils.timezone import now
import openpyxl
from django.core.mail import EmailMultiAlternatives, EmailMessage
import io
import logging
from django.db import IntegrityError
from orjson import loads
from xhtml2pdf import pisa
import natsort
import datetime as dt
from django.conf import settings
from djoser.conf import settings as djoser_settings
from djoser.compat import get_user_email
from djoser.serializers import SendEmailResetSerializer, PasswordResetConfirmRetypeSerializer
from django.contrib.auth.models import Permission
from django.db.models import F, Q, Value, CharField
from django.db.models.functions import Concat
from django.http import HttpResponse
from django.template.loader import render_to_string, get_template
from django_mysql.models import GroupConcat
from django.contrib.auth.tokens import default_token_generator
from rest_framework.decorators import action
from rest_framework.generics import get_object_or_404
from rest_framework.mixins import (RetrieveModelMixin, ListModelMixin, CreateModelMixin, UpdateModelMixin,
                                   DestroyModelMixin)
from rest_framework.viewsets import ReadOnlyModelViewSet, GenericViewSet
from rest_framework.views import APIView
from rest_framework import status, viewsets, views, permissions
from rest_framework.response import Response
from rest_framework_simplejwt.exceptions import TokenError, InvalidToken
from data_portal.api.response import Response as ResponseV2
from data_portal.api.utils import (active_datasets, report_timeframe, reports_active_datasets,
                                   reports_ticker_query_condition, reports_footer_text, ticker_default_pf)
from user_authentication.models import (PermissionsModel, CustomUserPerm, SubscribeReports, UserReportDetails,
                                        BBIWebAuthUser, BBIWebPermClients)
from user_authentication.permissions import IsAdmin, IsAuth, IsSuperAdmin, IsOtpVerified
from rest_framework.permissions import AllowAny
from user_authentication.utils import (GenericAPIException, DATASET_PERMS, reports_mapping, SendReportsThread,
                                       dataset_template_mapping, xls_mapping, report_headers, GenerateReportsThread)
from history.models import UserLoginHistory, UserDatasetPermRequest
from django.contrib.auth import get_user_model
from django.core.exceptions import ObjectDoesNotExist
from user_authentication.serializers import (UserSerializer, UserUpdateSerializer, PermissionSerializer,
                                             GroupsUserSerializer, UpdateProfileSerializer, ReferralEmailSerializer,
                                             Admins, CustomUserPermSerializer, UsersList, SubscribeReportsSerializer,
                                             DatasetPermRequestSerializer, UserReportDetailsSerializer,
                                             SubscribeReportsAdminSerializer, SendApprovedReportsSerializer,
                                             UploadReportsSerializer, ReadNotificationSerializer)
from rest_framework_simplejwt.views import TokenObtainPairView
from user_authentication.serializers import (CustomTokenObtainPairSerializer, ValidateReportsArguments,
                                             CustomTokenRefreshSerializer, NewDapCustomTokenObtainPairSerializer,
                                             NewDapCustomTokenRefreshSerializer)
from user_authentication.utils import (get_request_meta, send_email_to_user, get_custom_jwt, get_custom_otp_jwt,
                                       reports_perm_mapping, bbi_permissions_mapping, bbi_other_permissions)
from user_authentication.paginations import Pagination
from user_authentication.tokens import CustomRefreshToken, CustomAccessToken
from google.cloud import storage, bigquery
from google.api_core.exceptions import BadRequest, Forbidden
from cryptography.fernet import Fernet, InvalidToken as CInvalidToken
from shopping_center.utils import unit_area
from self_storage.utils import ss_metrics
from wsgiref.util import FileWrapper
from constants import REPORT_MSAs
from django.utils import timezone
from django.contrib.auth.hashers import make_password
from django_otp import devices_for_user
from django_otp.plugins.otp_totp.models import TOTPDevice
import uuid
from .tokens import RefreshToken
from django.core.paginator import Paginator
from rest_framework_simplejwt.views import TokenRefreshView
import firebase_admin
from firebase_admin import credentials, firestore

logger = logging.getLogger('sentry_logger')
User = get_user_model()


# from concurrent.futures import ThreadPoolExecutor


class UsersViewSet(GenericViewSet, CreateModelMixin, RetrieveModelMixin, UpdateModelMixin, ListModelMixin):
    permission_classes = [IsAdmin]
    queryset = User.objects.all()
    serializer_class = UserSerializer
    user_perm_model = CustomUserPerm
    user_perm_serializer = CustomUserPermSerializer
    pagination_class = Pagination
    group_user_serializer_class = GroupsUserSerializer

    def get_permissions(self):
        return [IsSuperAdmin()] if self.is_users_admins() else [self.permission_classes[0]()]

    def get_object(self):
        lookup_url_kwarg = self.lookup_url_kwarg or self.lookup_field
        filter_kwargs = {self.lookup_field: self.kwargs[lookup_url_kwarg]}
        obj = get_object_or_404(self.get_queryset(), **filter_kwargs)
        return obj

    def get_admins(self):
        return self.queryset.filter(Q(is_staff=True) | Q(is_superuser=True))

    def get_companies(self):
        return self.queryset.values_list('company', flat=True).distinct().exclude(
            Q(company__isnull=True) | Q(company__exact=''))

    def get_users(self):
        queryset = self.queryset.filter(is_staff=False, is_superuser=False)
        if self.request.query_params.get('show_clients'):
            queryset = queryset.filter(is_client=self.refactor_param_val(self.request.query_params.get(
                'show_clients').lower()))
        if self.request.query_params.get('is_active'):
            queryset = queryset.filter(is_active=self.refactor_param_val(self.request.query_params.get(
                'is_active').lower()))
        return queryset

    @staticmethod
    def refactor_param_val(param):
        key_value = {'true': True, 'false': False}
        return key_value.get(param)

    @staticmethod
    def get_bucket_perm_response(errors):
        response = list()
        if errors['assign']:
            response.append(
                f'There was an issue encountered when assigning permissions to permission ID(s) {errors["assign"]}')
        if errors['remove']:
            response.append(
                f'There was an issue encountered when removing permissions to permission ID(s) {errors["remove"]}')
        if errors['account']:
            response.append(
                f'{errors["account"][0]} is an invalid account for assigning bucket role')
        return ' and '.join(response) if response else None

    @staticmethod
    def create_user_reports_subscriptions_if_not_exists(model, user):
        for dataset in list(active_datasets.keys()):
            model.objects.create(user=user, dataset=dataset)

    @staticmethod
    def create_new_datasets_reports_subscriptions_if_not_exists(model, user, dataset_to_be_subscribed):
        for dataset in dataset_to_be_subscribed:
            model.objects.create(user=user, dataset=dataset)

    def is_users_admins(self):
        admins = Admins(data=self.request.query_params)
        admins.is_valid(raise_exception=True)
        return admins.validated_data['admins']

    def get_queryset(self):
        queryset = self.get_admins() if self.is_users_admins() else self.get_users()
        filters = self.get_filters()
        if filters:
            try:
                queryset = queryset.filter(**filters)
            except Exception as e:
                raise GenericAPIException(detail=str(e))
        return queryset.order_by(*self.request.query_params.get('sort', '-date_joined').split(','))

    @staticmethod
    def update_gs_bucket_email(gcp_mail, gcp_new_mail, buckets_assigned=None):
        """remove previous email and add new mail in gcp iam to get bucket access."""
        if settings.ENV == 'production':
            buckets = ["client_singlefamily", "client_multifamily", "client_shoppingcenter", "client_selfstorage"]
            buckets_dict = {bucket: bucket for bucket in buckets}
        else:
            buckets = ["client_singlefamily_develop", "client_multifamily_develop", "client_shoppingcenter_develop",
                       "client_selfstorage_develop"]
            buckets_dict = {bucket.split("_develop")[0]: bucket for bucket in buckets}

        assigned_buckets = [buckets_dict[key] for key in buckets_assigned]
        user_gcp_project = settings.PROJECT_NAME
        role = "roles/storage.objectViewer"
        member = "user:{}".format(gcp_mail.lower())
        other_member = "user:{}".format(gcp_mail.capitalize())
        new_member = "user:{}".format(gcp_new_mail.lower())
        storage_client = storage.Client(credentials=settings.BUCKET_CREDENTIALS)
        for bucket in assigned_buckets:
            bucket = storage_client.bucket(bucket, user_project=user_gcp_project)
            policy = bucket.get_iam_policy(requested_policy_version=3)
            binding = next((b for b in policy.bindings if b["role"] == role), None)
            if binding:
                member = other_member if other_member in binding["members"] else member
                if member in binding["members"]:
                    binding["members"].discard(member)
                    binding["members"].add(new_member)
                    bucket.set_iam_policy(policy)

    @staticmethod
    def delete_gs_bucket_email(gcp_mail, buckets_assigned=None):
        """remove bucket access permission."""
        if settings.ENV == 'production':
            buckets = ["client_singlefamily", "client_multifamily", "client_shoppingcenter", "client_selfstorage"]
            buckets_dict = {bucket: bucket for bucket in buckets}
        else:
            buckets = ["client_singlefamily_develop", "client_multifamily_develop", "client_shoppingcenter_develop",
                       "client_selfstorage_develop"]
            buckets_dict = {bucket.split("_develop")[0]: bucket for bucket in buckets}

        assigned_buckets = [buckets_dict[key] for key in buckets_assigned]
        user_gcp_project = settings.PROJECT_NAME
        role = "roles/storage.objectViewer"
        member = "user:{}".format(gcp_mail.lower())
        other_member = "user:{}".format(gcp_mail.capitalize())
        storage_client = storage.Client(credentials=settings.BUCKET_CREDENTIALS)
        for bucket in assigned_buckets:
            bucket = storage_client.bucket(bucket, user_project=user_gcp_project)
            policy = bucket.get_iam_policy(requested_policy_version=3)
            binding = next((b for b in policy.bindings if b["role"] == role), None)
            if binding:
                member = other_member if other_member in binding["members"] else member
                if member in binding["members"]:
                    binding["members"].discard(member)
                    bucket.set_iam_policy(policy)

    @staticmethod
    def delete_all_gs_bucket_perm(gcp_mail, buckets_assigned=None):
        """remove bucket access permission."""
        if settings.ENV == 'production':
            buckets = ["client_singlefamily", "client_multifamily", "client_shoppingcenter", "client_selfstorage"]
            buckets_dict = {bucket: bucket for bucket in buckets}
        else:
            buckets = ["client_singlefamily_develop", "client_multifamily_develop", "client_shoppingcenter_develop",
                       "client_selfstorage_develop"]
            buckets_dict = {bucket.split("_develop")[0]: bucket for bucket in buckets}
        if buckets_assigned:
            assigned_buckets = [buckets_dict[key] for key in buckets_assigned]
        else:
            assigned_buckets = buckets_dict
        user_gcp_project = settings.PROJECT_NAME
        role = "roles/storage.objectViewer"
        member = "user:{}".format(gcp_mail.lower())
        other_member = "user:{}".format(gcp_mail.capitalize())
        storage_client = storage.Client(credentials=settings.BUCKET_CREDENTIALS)
        for bucket in assigned_buckets.values():
            bucket = storage_client.bucket(bucket, user_project=user_gcp_project)
            policy = bucket.get_iam_policy(requested_policy_version=3)
            binding = next((b for b in policy.bindings if b["role"] == role), None)
            if binding:
                member = other_member if other_member in binding["members"] else member
                if member in binding["members"]:
                    binding["members"].discard(member)
                    bucket.set_iam_policy(policy)

    def get_filters(self):
        params = self.request.query_params
        filters = {'company': 'company', 'id__in': 'users',
                   'last_login__date__gte': 'last_visit_from', 'last_login__date__lte': 'last_visit_to',
                   'date_joined__date__gte': 'created_date_from', 'date_joined__date__lte': 'created_date_to'}
        kwargs = {key: params.get(val) for key, val in filters.items() if params.get(val)}
        kwargs.update({"id__in": kwargs["id__in"].split(',')}) if kwargs.get('id__in') else None
        return kwargs

    @staticmethod
    def update_password(user, password, test_user=None):
        user.set_password(password)
        user.save()
        if not test_user and user.is_bbi_user:
            bbi_user = BBIWebAuthUser.objects.using('bbi_web').get(username=user.username)
            bbi_user.password = make_password(password)
            bbi_user.save(using='bbi_web')

    def perform_update(self, serializer, instance=None, profile=False):
        password, gcp_old_email = serializer.validated_data.pop('password', None), None
        # change_email = serializer.validated_data.get('email')
        if serializer.validated_data.get('gcp_email') == '' and instance.gcp_email and serializer.validated_data.get(
                'gcp_email') != instance.gcp_email:
            gcp_old_email = instance.gcp_email
            buckets_assigned = [perm['permissions']['Bucket Data']['name'] for key, perm in DATASET_PERMS.items() if
                                instance.has_perm(perm['permissions']['Bucket Data']['perm'])]
            if buckets_assigned:
                try:
                    self.delete_gs_bucket_email(instance.gcp_email, buckets_assigned)
                except (BadRequest, Forbidden, Exception):
                    raise GenericAPIException('error occur while updating gcp_email at bucket level')
                ids = [perm.id for perm in Permission.objects.filter(name__in=buckets_assigned)]
                CustomUserPerm.objects.filter(user=instance, permission_id__in=ids).delete()
        elif serializer.validated_data.get('gcp_email') and instance.gcp_email and serializer.validated_data.get(
                'gcp_email') != instance.gcp_email:
            gcp_old_email = instance.gcp_email
            buckets_assigned = [perm['permissions']['Bucket Data']['name'] for key, perm in DATASET_PERMS.items() if
                                instance.has_perm(perm['permissions']['Bucket Data']['perm'])]
            if buckets_assigned:
                try:
                    self.update_gs_bucket_email(instance.gcp_email, serializer.validated_data.get('gcp_email'),
                                                buckets_assigned)
                except (BadRequest, Forbidden, Exception):
                    raise GenericAPIException('error occur while updating gcp_email at bucket level')
        elif serializer.validated_data.get('email') and self.queryset.filter(
                Q(email=serializer.validated_data.get('email')) & ~Q(id=instance.id)).exists():
            raise GenericAPIException('user with given email already exists. Please try with other email.')
        serializer.save(gcp_old_email=gcp_old_email) if gcp_old_email else serializer.save()
        user_updated_data = {'Username': instance.username,
                             'Email': serializer.validated_data.get('email')}
        if serializer.validated_data.get('gcp_email'):
            user_updated_data.update({'GCP Email': serializer.validated_data.get('gcp_email')})
        if not password:
            self.send_email(user=instance, _for='update_profile',
                            user_updated_data=user_updated_data)
        if instance.is_bbi_user and not self.request.user.username == 'test_cases_user':
            bbi_user = BBIWebAuthUser.objects.using('bbi_web').get(username=instance.username)
            if self.request.data.get('first_name'):
                bbi_user.first_name = self.request.data.get('first_name')
            if self.request.data.get('email'):
                bbi_user.email = self.request.data.get('email')
            bbi_user.save(using='bbi_web')
        # user = serializer.save(gcp_old_email=gcp_old_email) if gcp_old_email else serializer.save()
        # if change_email:
        #     try:
        #         user.username = user.email
        #         user.save()
        #     except IntegrityError:
        #         raise GenericAPIException('user with given email already exists. Please try with other email.')
        #     except Exception as e:
        #         raise GenericAPIException(str(e))
        if password and not profile:
            if self.request.user.username == 'test_cases_user':
                self.update_password(instance, password, test_user=True)
            else:
                self.update_password(instance, password)
                user_updated_data.update({'Password': password})
                self.send_email(user=instance, _for='update_profile',
                                user_updated_data=user_updated_data)

    def list(self, request, *args, **kwargs):
        if request.query_params.get('download') and request.query_params.get('download').lower() == 'true':
            return self.download_csv()
        if self.request.query_params.get('listed') and self.request.query_params.get('listed').lower() == 'true':
            queryset = self.get_queryset().values('id', 'username', full_name=Concat(
                F('first_name'), Value(' '), F('last_name'), output_field=CharField()))
            if self.request.query_params.get('listed_user'):
                queryset = queryset.filter(Q(full_name__icontains=self.request.query_params.get(
                    'listed_user')) | Q(username__icontains=self.request.query_params.get('listed_user')))
            return Response(queryset)
        return super().list(self, request, *args, **kwargs)

    @action(detail=True, permission_classes=[IsAdmin], methods=['get'])
    def permissions(self, request, *args, **kwargs):
        lookup_url_kwarg = self.lookup_url_kwarg or self.lookup_field
        filter_kwargs = {self.lookup_field: self.kwargs[lookup_url_kwarg]}
        user = get_object_or_404(self.get_users(), **filter_kwargs)
        context = self.get_serializer_context()
        context['perms'] = 'perms_with_ids'
        data = self.serializer_class(user, context=context).data
        return Response(data)

    @action(detail=False, permission_classes=[IsAuth], methods=['get'])
    def me(self, request, *args, **kwargs):
        context = self.get_serializer_context()
        context['perms'] = 'only_perms'
        data = self.serializer_class(request.user, context=context).data
        return Response(data)

    @action(detail=False, permission_classes=[IsAuth], methods=['get', 'put'])
    def subscribe_reports(self, request, *args, **kwargs):
        if self.request.method == 'GET':
            if not SubscribeReports.objects.filter(user=request.user).exists():
                self.create_user_reports_subscriptions_if_not_exists(SubscribeReports, request.user)
            subscribe_datasets = list(
                SubscribeReports.objects.filter(user=request.user).values_list('dataset', flat=True))
            all_active_datasets = list(reports_active_datasets.keys())
            if subscribe_datasets != all_active_datasets:
                dataset_to_be_subscribed = list(set(all_active_datasets).difference(set(subscribe_datasets)))
                self.create_new_datasets_reports_subscriptions_if_not_exists(SubscribeReports, request.user,
                                                                             dataset_to_be_subscribed)
            queryset = SubscribeReports.objects.filter(user=request.user).values()
            queryset = {
                item['dataset']: {
                    'weekly': item['weekly'], 'monthly': item['monthly'], 'snooze': item['snooze']
                } for item in queryset
            }
            response = {'datasets': all_active_datasets, 'reports': queryset}
            return Response(response)
        if self.request.method == 'PUT':
            serializer = SubscribeReportsSerializer(
                SubscribeReports, data=request.data, context=self.get_serializer_context())
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(status=status.HTTP_200_OK)

    @action(detail=False, permission_classes=[AllowAny], methods=['get', 'put'])
    def subscribe_reports_admin(self, request, *args, **kwargs):
        if self.request.method == 'GET':
            user_id = self.request.query_params.get('user_id')
            try:
                user = User.objects.get(id=user_id)
            except User.DoesNotExist:
                return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)
            except Exception as e:
                return Response({'error': 'please enter a valid user id here'}, status=status.HTTP_404_NOT_FOUND)
            if not SubscribeReports.objects.filter(user=user).exists():
                self.create_user_reports_subscriptions_if_not_exists(SubscribeReports, user)
            subscribe_datasets = list(
                SubscribeReports.objects.filter(user=user).values_list('dataset', flat=True))
            all_active_datasets = list(reports_active_datasets.keys())
            if subscribe_datasets != all_active_datasets:
                dataset_to_be_subscribed = list(set(all_active_datasets).difference(set(subscribe_datasets)))
                self.create_new_datasets_reports_subscriptions_if_not_exists(SubscribeReports, user,
                                                                             dataset_to_be_subscribed)
            queryset = SubscribeReports.objects.filter(user=user).values()
            queryset = {
                item['dataset']: {
                    'weekly': item['weekly'], 'monthly': item['monthly'], 'snooze': item['snooze']
                } for item in queryset
            }
            response = {'reports': queryset}
            return Response(response)
        if self.request.method == 'PUT':
            serializer = SubscribeReportsAdminSerializer(
                SubscribeReports, data=request.data, context=self.get_serializer_context())
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(status=status.HTTP_200_OK)

    @action(detail=False, permission_classes=[IsAuth], methods=['post'])
    def update_profile(self, request):
        serializer = UpdateProfileSerializer(data=request.data, context=self.get_serializer_context())
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer, instance=self.request.user, profile=True)
        return self.me(request)

    @action(detail=False, permission_classes=[IsAuth], methods=['get'])
    def companies(self, request, *args, **kwargs):
        companies = self.get_companies()
        if request.query_params.get('company'):
            companies = companies.filter(company__icontains=request.query_params.get('company'))
        else:
            companies = companies[:15]
        return Response(companies)

    @action(detail=False, permission_classes=[IsAuth], methods=['get'])
    def is_users_exist(self, request, *args, **kwargs):
        if request.query_params.get('username'):
            username_exist = {"is_exist": self.queryset.filter(username=request.query_params.get('username')).exists()}
        else:
            username_exist = self.queryset.values_list('username', flat=True)
        return Response(username_exist)

    @staticmethod
    def modify_gs_bucket_policy(gcp_mail, bucket_name, errors, add_member=True):
        """Add or Remove a member to a role binding."""
        user_gcp_project = settings.PROJECT_NAME
        role = "roles/storage.objectViewer"
        member = "user:{}".format(gcp_mail.lower())
        other_member = "user:{}".format(gcp_mail.capitalize())
        storage_client = storage.Client(credentials=settings.BUCKET_CREDENTIALS)
        bucket = storage_client.bucket(bucket_name, user_project=user_gcp_project)
        policy = bucket.get_iam_policy(requested_policy_version=3)
        if add_member:
            binding = next((b for b in policy.bindings if b["role"] == role), None)
            if binding is None:
                binding = {
                    "role": role,
                    "members": [member],
                }
                policy.bindings.append(binding)
            else:
                binding["members"].add(member)
            try:
                policy_updated = bucket.set_iam_policy(policy)
            except (BadRequest, Forbidden, Exception):
                errors['account'].append(member)
            updated_binding = next(b for b in policy_updated.bindings if b["role"] == role)
            if member not in updated_binding["members"] and other_member not in updated_binding["members"]:
                raise GenericAPIException('email address must be associated with valid google account')
        else:
            binding = next((b for b in policy.bindings if b["role"] == role and b.get("condition") is None), None)
            if binding:
                member = other_member if other_member in binding["members"] else member
                if member in binding["members"]:
                    binding["members"].discard(member)
                    bucket.set_iam_policy(policy)

    @staticmethod
    def update_subscription_reports(user, removed_ids):
        subscribe_reports = list(SubscribeReports.objects.filter(user=user).values())
        if subscribe_reports:
            unsub_reports = list(
                PermissionsModel.objects.filter(Q(name__startswith='view'), Q(id__in=removed_ids)).values_list('name',
                                                                                                               flat=True))
            updated_list = [reports_perm_mapping.get(value, value) for value in unsub_reports]
            SubscribeReports.objects.filter(user=user, dataset__in=updated_list).update(weekly=False, monthly=False)

    @action(detail=False, permission_classes=[IsAdmin], methods=['post'])
    def create_or_update_permission(self, request, *args, **kwargs):
        serializer = self.user_perm_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user, permissions = serializer.validated_data['user'], serializer.validated_data['permissions']
        permission_view = PermissionsViewSet()
        permission_view.request = request
        available_permissions = set(permission_view.get_permissions_list_without_bucket())
        to_be_assigned = {p.id for p in permissions}
        if to_be_assigned:
            if not to_be_assigned.issubset(available_permissions):
                raise GenericAPIException('invalid permission id(s) in permissions list')
        to_be_removed = available_permissions.difference(to_be_assigned)
        _all = serializer.validated_data['all']
        if not user.is_active:
            raise GenericAPIException('user is not active')
        if _all:
            self.all_api_permissions(user, period_end=serializer.validated_data['period_end'])
        else:
            for perm in to_be_assigned:
                kwargs = dict(permission_id=perm, user=user, period_end=serializer.validated_data['period_end'])
                self.user_perm_model.objects.update_or_create(permission_id=perm, user=user, defaults=kwargs)

            if user.is_bbi_user:
                available_permissions_bbi = set(permission_view.get_only_dataset_permissions_for_bbi_user())
                available_permissions_bbi.update(bbi_other_permissions.values())
                to_be_assigned_bbi = {p.name for p in permissions if p.name in available_permissions_bbi}
                to_be_removed_bbi = available_permissions_bbi.difference(to_be_assigned_bbi)
                bbi_user = BBIWebAuthUser.objects.using('bbi_web').get(username=user.username)
                assign_perms_dict = dict()
                remove_perms_dict = dict()
                # since this perm is not in bbi_web
                to_be_assigned_bbi.discard('view_manufactured_housing_dashboard')
                to_be_removed_bbi.discard('view_manufactured_housing_dashboard')
                for perm in to_be_assigned_bbi:
                    assign_perms_dict[bbi_permissions_mapping.get(perm)] = datetime.strptime(
                        settings.PERMISSIONS_EXPIRY_DATE, '%Y-%m-%d').date()
                BBIWebPermClients.objects.using('bbi_web').filter(userId=bbi_user.id).update(**assign_perms_dict)
                for perm in to_be_removed_bbi:
                    if perm.startswith('view_'):
                        remove_perms_dict[bbi_permissions_mapping.get(perm)] = datetime.today().date() - timedelta(
                            days=1)
                    else:
                        remove_perms_dict[bbi_other_permissions.get(perm)] = datetime.today().date() - timedelta(days=1)
                BBIWebPermClients.objects.using('bbi_web').filter(userId=bbi_user.id).update(**remove_perms_dict)

            self.user_perm_model.objects.filter(user=user, permission_id__in=to_be_removed).delete()
        self.update_subscription_reports(user, to_be_removed)
        # self.send_email(user=user, _for='permission')
        return Response(status=status.HTTP_200_OK)

    @action(detail=False, permission_classes=[IsAdmin], methods=['post'])
    def create_or_update_gcp_permission(self, request, *args, **kwargs):
        serializer = self.user_perm_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user, permissions = serializer.validated_data['user'], serializer.validated_data['permissions']
        if not user.is_active:
            raise GenericAPIException('user is not active')
        if not user.gcp_email:
            raise GenericAPIException('user does not have GCP Email')
        perm_obj = PermissionsViewSet()
        avail_perm_ids = {perm_obj.get_permission_ids(key).get('Bucket Data') for key in DATASET_PERMS if
                          perm_obj.get_permission_ids(key).get('Bucket Data') is not None}
        to_be_assigned = {p.id for p in permissions}
        already_assigned = set(
            self.user_perm_model.objects.filter(user=user, permission_id__in=avail_perm_ids).values_list(
                'permission_id', flat=True))
        if to_be_assigned:
            if not to_be_assigned.issubset(avail_perm_ids):
                raise GenericAPIException('invalid permission id(s) in permissions list')
        to_be_removed = already_assigned.difference(to_be_assigned)
        to_be_assigned = to_be_assigned.difference(already_assigned)
        permissions_remove = list(PermissionsModel.objects.filter(id__in=to_be_removed).values_list('id', 'name'))
        permissions_assign = list(PermissionsModel.objects.filter(id__in=to_be_assigned).values_list('id', 'name'))
        errors = dict(assign=list(), remove=list(), account=list())
        for perm_id, perm in permissions_assign:
            if not settings.ENV == 'production':
                perm = f'{perm}_develop'
            try:
                self.modify_gs_bucket_policy(user.gcp_email, perm, errors)
            except (BadRequest, Forbidden, Exception):
                errors['assign'].append(perm_id)
            else:
                kwargs = dict(user=user, permission_id=perm_id, period_end=serializer.validated_data['period_end'])
                self.user_perm_model.objects.update_or_create(permission_id=perm_id, user=user, defaults=kwargs)
        for perm_id, perm in permissions_remove:
            if not settings.ENV == 'production':
                perm = f'{perm}_develop'
            try:
                self.modify_gs_bucket_policy(user.gcp_email, perm, errors, add_member=False)
            except (BadRequest, Forbidden, Exception):
                errors['remove'].append(perm_id)
            else:
                self.user_perm_model.objects.filter(user=user, permission_id=perm_id).delete()
        err_response = self.get_bucket_perm_response(errors)
        if err_response:
            return Response(data=err_response, status=status.HTTP_400_BAD_REQUEST)
        # self.send_email(user=user, _for='permission')
        return Response(status=status.HTTP_200_OK)

    @action(detail=False, permission_classes=[IsAdmin], methods=['post'])
    def del_users(self, request, *args, **kwargs):
        users = UsersList(data={'users': request.data.get('users')})
        users.is_valid(raise_exception=True)
        queryset = self.get_queryset().filter(id__in=users.validated_data['users'])
        data = list(self.get_queryset().filter(id__in=users.validated_data['users']).values('username', 'gcp_email'))
        dap_users_names = [value['username'] for value in data]
        gcp_emails = [value['gcp_email'] for value in data if value['gcp_email'] is not None]
        if len(gcp_emails) > 0:
            for gcp_email in gcp_emails:
                self.delete_all_gs_bucket_perm(gcp_email)
        if queryset:
            queryset.delete()
        else:
            return Response(data="no user found", status=status.HTTP_400_BAD_REQUEST)
        # Delete users from the bbi_web database
        if dap_users_names and not request.user.username == 'test_cases_user':
            bbi_web_user_ids = BBIWebAuthUser.objects.using('bbi_web').filter(username__in=dap_users_names).values_list(
                'id', flat=True)
            BBIWebPermClients.objects.using('bbi_web').filter(userId__in=bbi_web_user_ids).delete()
            BBIWebAuthUser.objects.using('bbi_web').filter(username__in=dap_users_names).delete()
        return Response(status=status.HTTP_200_OK)

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = UserUpdateSerializer(instance, data=request.data, partial=True,
                                          context=self.get_serializer_context())
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer, instance)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        request_data = request.data if hasattr(request, 'data') else request.POST
        serializer = self.get_serializer(data=request_data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        try:
            password = data.pop('password')
            user = User(**data)
            user.is_bbi_user = True if not self.request.user.username == 'test_cases_user' else False
            user.set_password(password)
            user.save()
            if not self.request.user.username == 'test_cases_user':
                bbi_user = BBIWebAuthUser(
                    username=data.get('username'),
                    email=data.get('email'),
                    first_name=data.get('first_name'),
                    is_staff=data.get('is_staff', False),
                    is_active=True,
                    is_superuser=data.get('is_superuser', False),
                    date_joined=date.today()
                )
                bbi_user.password = make_password(password)
                bbi_user.save(using='bbi_web')
                bbi_web_perm = BBIWebPermClients.objects.using('bbi_web').create(
                    userId=bbi_user.id,
                    multifamily=datetime.today().date() - timedelta(days=1),
                    singlefamily=datetime.today().date() - timedelta(days=1),
                    self_storage=datetime.today().date() - timedelta(days=1),
                    studenthousing=datetime.today().date() - timedelta(days=1),
                    homebuilders=datetime.today().date() - timedelta(days=1),
                    shopping_centers=datetime.today().date() - timedelta(days=1),
                    office_reits=datetime.today().date() - timedelta(days=1)
                )
                bbi_web_perm.save(using='bbi_web')
        except Exception as e:
            raise GenericAPIException(str(e))
        if user.is_staff or serializer.initial_data.get('all_permissions'):
            self.all_api_permissions(user, period_end=serializer.initial_data.get('period_end'))
        elif serializer.initial_data.get('permissions_list'):
            serializer = (self.user_perm_serializer(data={
                'permissions': serializer.initial_data['permissions_list'], 'user': user.id,
                'period_end': serializer.initial_data['period_end']}) if serializer.initial_data.get('period_end') else
                          self.user_perm_serializer(data={'permissions': serializer.initial_data['permissions_list'],
                                                          'user': user.id}))
            serializer.is_valid(raise_exception=True)
            permissions = serializer.validated_data['permissions']
            for perm in permissions:
                kwargs = dict(permission=perm, user=user, period_end=serializer.validated_data['period_end'])
                self.user_perm_model.objects.update_or_create(permission=perm, user=user, defaults=kwargs)
        self.send_email(user=user, _for='account', password=password)
        return Response(status=status.HTTP_201_CREATED)

    def download_csv(self):
        try:
            queryset = list(self.get_users().values_list("username", "first_name", "last_name", "email", "company",
                                                         "date_joined", "last_login").order_by('-date_joined'))
            response = HttpResponse(content_type="text/csv")
            response[
                "Content-Disposition"] = f'attachment; filename=users_{datetime.now().strftime("%Y-%m-%d-%H%M%S")}.csv'
            writer = csv.writer(response)
            writer.writerow(['Username', 'First Name', 'Last Name', 'Email', 'Company', 'Created Date', 'Last Visit'])
            writer.writerows(queryset)
        except ValueError:
            response = Response(data={"details": 'empty dataset in not downloadable'}, status=400)
        except Exception as e:
            response = Response(data={"details": str(e)}, status=400)
        return response

    def send_email(self, user, _for, password=None, user_updated_data=None):
        send_email = self.request.query_params.get('send_email') if hasattr(
            self.request, 'query_params') else self.request.GET.get('send_email')
        if send_email and send_email.lower() == 'true':
            if user.email:
                context = self.get_serializer_context()
                context.update({'perms': 'perms_with_ids'})
                data = self.serializer_class(instance=user, context=context).data
                permissions = data['permissions'] if True in [data['permissions'][d]['Dashboard']['status'] for d in
                                                              data['permissions'].keys()] else {}
                if user.is_new_dap_user:
                    send_email_to_user(to_email=[user.email],
                                       data={'permissions': None, 'username': user.username,
                                             'password': password,
                                             'bbi_website': settings.NEWDAP_WEBSITE_URL,
                                             'user_updated_data': user_updated_data},
                                       _for=_for)
                else:
                    send_email_to_user(to_email=[user.email],
                                       data={'permissions': permissions, 'username': user.username,
                                             'password': password,
                                             'bbi_website': settings.BBI_WEBSITE_URL,
                                             'user_updated_data': user_updated_data},
                                       _for=_for)

    def all_api_permissions(self, user, period_end=None, assign=True):
        permissions = PermissionsViewSet()
        permissions.request = self.request
        permissions_list = permissions.get_permissions_list_without_bucket()
        if not assign:
            self.user_perm_model.objects.filter(user=user, permission_id__in=permissions_list).delete()
            return
        for perm in permissions_list:
            kwargs = dict(permission_id=perm, user=user, period_end=period_end)
            self.user_perm_model.objects.update_or_create(permission_id=perm, user=user, defaults=kwargs)


class PermissionsViewSet(ReadOnlyModelViewSet):
    http_method_names = ['get']
    permission_classes = [IsAdmin]
    queryset = PermissionsModel.objects.all()
    serializer_class = PermissionSerializer

    def list(self, request, *args, **kwargs):
        response = self.get_response()
        return Response(response)

    @staticmethod
    def get_permission_ids(dataset):
        permissions = DATASET_PERMS[dataset]['permissions']
        try:
            permissions = {
                key: Permission.objects.get(name=val["name"]).id for key, val in permissions.items()
            }
        except ObjectDoesNotExist:
            return {}
        return permissions

    def get_response(self):
        permissions = {key: self.get_permission_ids(dataset=key) for key in DATASET_PERMS}
        return permissions

    def get_permissions_list(self):
        permissions = self.get_response()
        permissions_list = list()
        for key, val in permissions.items():
            permissions_list.extend(list(val.values()))
        return permissions_list

    def get_permissions_list_without_bucket(self):
        permissions = list(self.queryset.filter(
            Q(name__startswith='view') | Q(name__startswith='access') | Q(name__startswith='download')).values_list(
            'id', flat=True))
        return permissions

    def get_only_dataset_permissions_for_bbi_user(self):
        permissions = list(self.queryset.filter(
            Q(name__startswith='view')).values_list('name', flat=True))
        return permissions


class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except TokenError as e:
            raise InvalidToken(e.args[0])

        return ResponseV2(serializer.validated_data, status=status.HTTP_200_OK)


class NewDapCustomTokenObtainPairView(CustomTokenObtainPairView):
    serializer_class = NewDapCustomTokenObtainPairSerializer


class LogoutView(APIView):
    permission_classes = (IsAuth,)

    def update_logout(self):
        meta = get_request_meta(self.request)
        try:
            login_history = UserLoginHistory.objects.get(user=self.request.user, ip_address=meta['ip_address'],
                                                         user_agent=meta['user_agent'],
                                                         token=bytes(self.request.auth.token).decode())
            login_history.logout_time = datetime.now()
            login_history.save()
        except ObjectDoesNotExist:
            pass

    def post(self, request):
        refresh_token = request.data.get("refresh_token")
        if not refresh_token:
            raise GenericAPIException('refresh_token is required')
        try:
            refresh_token = CustomRefreshToken(refresh_token)
            access_token = CustomAccessToken(bytes(request.auth.token).decode())
            access_token.blacklist()
            refresh_token.blacklist()
            self.update_logout()
            return Response(data={'details': 'logout successfully'}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(data={'details': str(e)}, status=status.HTTP_400_BAD_REQUEST)


class ReferEmailView(APIView):
    http_method_names = ['post']
    permission_classes = [IsAuth]
    serializer_class = ReferralEmailSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        signup_link = f'https://{settings.DOMAIN}/signup/'
        name = self.get_ref_name()
        context = {'message': data['message'], 'signup_link': signup_link, 'ref_username': name}
        send_email_to_user(to_email=data['emails'], data=context, _for='refer')
        return Response(status=status.HTTP_200_OK)

    def get_ref_name(self):
        name = f'{self.request.user.first_name} {self.request.user.last_name}' if (
                self.request.user.first_name and self.request.user.last_name) else self.request.user.first_name if (
            self.request.user.first_name) else self.request.user.last_name if self.request.user.last_name else (
            self.request.user.username)
        return name


class SendReports(APIView):
    queryset = SubscribeReports.objects.all()
    gcp_project = 'webdata-207211'

    def authenticate_cloud_request(self):
        fernet = Fernet(settings.REPORT_API_KEY)
        try:
            value = self.request.META['HTTP_CLOUD_AUTHORIZATION']
        except KeyError:
            raise GenericAPIException('permission denied', status_code=403)
        try:
            decrypt_value = fernet.decrypt(value).decode()
        except CInvalidToken:
            raise GenericAPIException('invalid token', status_code=401)
        if decrypt_value != settings.REPORT_API_TOKEN:
            raise GenericAPIException('invalid token', status_code=401)

    def post(self, request):
        self.authenticate_cloud_request()
        interval = request.query_params.get('interval')
        if interval not in ('weekly', 'monthly'):
            raise GenericAPIException('invalid interval')
        SendReportsThread(view=self, interval=interval).start()
        # self.send_reports(interval)
        return Response(status=status.HTTP_200_OK)

    def send_reports(self, interval, all_subscriber=None, datasets=None, send_to=None):
        pdfs = dict()
        kwargs = {interval: True, 'snooze': False}
        users = self.queryset.filter(**kwargs).values('user').annotate(datasets=GroupConcat('dataset'))
        users_with_datasets = {item['user']: item['datasets'].split(',') for item in users}
        if users_with_datasets:
            datasets = set(self.queryset.filter(**kwargs).values_list('dataset', flat=True).distinct())
            client = bigquery.Client(project=self.gcp_project)
            try:
                all_data = self.fetch_data(interval, client, datasets)
            except Exception as e:
                logger.error(f'All reports sending failed due to error occurred in data fetching. Reason: {str(e)}')
                return
            attached_file = 0
            for dataset in datasets:
                i = 0
                html_page = ""
                context, dates, msa_data, msa_dates = (
                    all_data[dataset]['data'], all_data[dataset]['dates'], all_data[dataset]['msa_data'],
                    all_data[dataset]['msa_dates'])

                unchanged_tickers, max_analysis_date = (
                    all_data[dataset]['unchanged_tickers'], all_data[dataset]['max_analysis_date'])
                outdated_tickers_list = [ticker for ticker in unchanged_tickers.keys()]
                data_as_of_text = self.get_data_as_of_text(unchanged_tickers, max_analysis_date, interval)

                report_header = report_headers[dataset][interval]
                for metric in all_data[dataset]['data'].keys():
                    i = i + 1
                    if context:
                        if dataset == 'SELF STORAGE':
                            context_data = {
                                'data': context[metric], 'dataset': dataset, 'interval': interval,
                                'metric': metric,
                                'dates': dates,
                                'created_date': max_analysis_date,
                                'published_date': datetime.today().date(),
                                'data_as_of_text': data_as_of_text,
                                'outdated_tickers_list': outdated_tickers_list,
                                'report_header': report_header,
                                'improved_msa': msa_data[metric]['result'],
                                'distinct_msa': msa_data[metric]['distinct_msa'],
                                'ticker_web_result': msa_data[metric]['ticker_web_result'],
                                'msa_dates': msa_dates[metric]['msa_dates'],
                                'ticker_web_date': msa_dates[metric]['ticker_web_date'],
                                'ss_metrics': ss_metrics,
                                'count': i
                            }
                        elif dataset == 'SENIOR LIVING':
                            context_data = {
                                'data': context[metric], 'dataset': dataset, 'interval': interval,
                                'metric': metric, 'dates': dates,
                                'created_date': max_analysis_date,
                                'published_date': datetime.today().date(),
                                'data_as_of_text': data_as_of_text,
                                'outdated_tickers_list': outdated_tickers_list,
                                'report_header': report_header,
                                'improved_msa': msa_data[metric],
                                'msa_dates': msa_dates[metric],
                                'beds_data': all_data[dataset]['beds_data'][metric],
                                'count': i
                            }
                        else:
                            context_data = {
                                'data': context[metric], 'dataset': dataset, 'interval': interval,
                                'metric': metric,
                                'dates': dates,
                                'created_date': max_analysis_date,
                                'published_date': datetime.today().date(),
                                'data_as_of_text': data_as_of_text,
                                'outdated_tickers_list': outdated_tickers_list,
                                'report_header': report_header,
                                'improved_msa': msa_data[metric],
                                'msa_dates': msa_dates[metric],
                                'count': i
                            }
                            if dataset == 'OFFICE':
                                context_data['ticker_improved_msa'] = all_data[dataset]['ticker_msa_data'][metric]
                        if dataset == 'SHOPPING CENTER':
                            context_data['unit_area'] = unit_area
                        template = self.map_template(dataset)
                        metric_page = render_to_string(template, context=context_data)
                        if metric_page:
                            html_page += '\n' + metric_page
                if dataset in ['SINGLEFAMILY', 'MULTIFAMILY']:
                    disclaimer_template = render_to_string("disclaimer_page.html")
                    html_page += '\n' + disclaimer_template
                converted, pdf = self.render_to_pdf(html_page)
                self.save_report_files_url(dataset, interval, pdf, datetime.today().now())
                if converted:
                    pdfs[dataset] = pdf

            for user_id, datasets_list in users_with_datasets.items():
                user_email = User.objects.get(pk=user_id).email
                if user_email:
                    email = EmailMultiAlternatives(f'{interval.capitalize()} Research Reports',
                                                   'Please find attached reports.',
                                                   settings.EMAIL_HOST_USER, [user_email])
                    if pdfs:
                        for dataset in datasets_list:
                            email.attach(f'{dataset}_{interval.upper()}_REPORT.pdf', pdfs[dataset], 'application/pdf')
                            workbook_data = io.BytesIO()
                            try:
                                self.save_data_to_workbook(xls_mapping, client, workbook_data, dataset, interval)
                            except Exception as e:
                                logger.error(
                                    f'xlsx data insertion failed. Reason: {str(e)}')
                            email.attach('{}.xlsx'.format(dataset), workbook_data.getvalue(),
                                         'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                            workbook_data.close()
                            attached_file += 1
                        if attached_file:
                            try:
                                email.send()
                                logger.info(
                                    f'sending email to this email: {user_email}')
                            except Exception as e:
                                logger.error(
                                    f'report(s) sending failed to email address: {user_email}. Reason: {str(e)}')
                else:
                    logger.error(
                        f'report(s) sending failed to email address: {user_email}. Reason: Files not attached')

    def map_template(self, dataset):
        return dataset_template_mapping[dataset]

    def save_report_files_url(self, dataset, interval, pdf_file, published_at, file_type=None,
                              approved=False, sent_at=None, history_upload=False):
        try:
            filename = settings.BUCKET_FOLDER_REPORTS + self.get_report_file_name(dataset, interval, file_type)
            self.save_reports_to_bucket(settings.GS_REPORT_BUCKET_NAME, filename, pdf_file, file_type, history_upload)
            report_name = self.get_report_name(interval, published_at)
            columns = {"report_name": report_name, "report_year": report_name.split('-')[-1],
                       "approved": approved, "dataset": dataset, "interval": interval, "published_at": published_at}
            if history_upload:
                columns.update({"status": 'Sent', "sent_at": sent_at})
            if not UserReportDetails.objects.filter(dataset=dataset, interval=interval,
                                                    report_name=report_name).exists():
                if file_type == 'pdf':
                    columns.update({"file_path": filename})
                elif file_type == 'xlsx':
                    columns.update({"xlsx_file_path": filename})
                user_report = UserReportDetails(**columns)
                user_report.save()
            else:
                user_report = UserReportDetails.objects.get(dataset=dataset, interval=interval, report_name=report_name)
                if file_type == 'pdf':
                    user_report.file_path = filename
                elif file_type == 'xlsx':
                    user_report.xlsx_file_path = filename
                user_report.published_at = published_at
                user_report.approved = approved
                user_report.report_year = report_name.split('-')[-1]
                user_report.save()
        except Exception as e:
            logger.error(f'report(s) uploading failed. Reason: {str(e)}')

    def save_reports_to_bucket(self, bucket_name, file_name, file_content, file_type=None, history_upload=False):
        client = storage.Client()
        bucket = client.bucket(bucket_name)
        blob = bucket.blob(file_name)
        try:
            if not history_upload:
                if file_type == 'pdf':
                    blob.upload_from_string(file_content, content_type='application/pdf')
                elif file_type == 'xlsx':
                    blob.upload_from_string(file_content,
                                            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            else:
                blob.upload_from_file(file_content)
        except Exception as e:
            logger.error(f'report(s) uploading failed. Reason: {str(e)}')

    def get_report_name(self, interval, published_at):
        if not isinstance(published_at, datetime):
            published_at = datetime.strptime(str(published_at), "%Y-%m-%d")

        target_date = published_at if published_at else date.today()
        iso_year, iso_month, iso_week = self.get_majority_month_of_iso_week(target_date)

        if interval == 'monthly':
            return f"{iso_month}-{iso_year}"
        elif interval == 'weekly':
            return f"{iso_month}-{iso_week}-week-{iso_year}"

    def get_majority_month_of_iso_week(self, target_date):
        iso_year, iso_week, iso_weekday = target_date.isocalendar()
        # Get the start of the ISO week (Monday)
        start_of_week = target_date - timedelta(days=iso_weekday)
        # Collect all 7 days of the ISO week
        week_days = [start_of_week + timedelta(days=i) for i in range(7)]
        # Count the occurrences of each month in the week
        month_counts = {}
        for day in week_days:
            month_counts[day.month] = month_counts.get(day.month, 0) + 1
        # Find the month with the most days in the week
        majority_month = max(month_counts, key=month_counts.get)
        majority_month_name = target_date.replace(month=majority_month).strftime("%B")
        return iso_year, majority_month_name, iso_week


    def get_report_file_name(self, dataset, interval, file_type=None):
        today = datetime.now()
        day = today.date().day
        file_name = None
        file_interval = None
        dataset = str(dataset).replace(' ', '_')
        if interval == 'weekly':
            if day in range(1, 9):
                file_interval = f"weekly_1"
            elif day in range(9, 16):
                file_interval = f"weekly_2"
            elif day in range(16, 23):
                file_interval = f"weekly_3"
            elif day in range(23, 32):
                file_interval = f"weekly_4"
        elif interval == 'monthly':
            file_interval = f"monthly_{today.date().month}"
        if file_type == "pdf":
            file_name = f"{dataset}/{interval.lower()}/{today.strftime('%Y-%m-%d-%H%M%S')}_{file_interval.upper()}.pdf"
        elif file_type == "xlsx":
            file_name = f"{dataset}/{interval.lower()}/{today.strftime('%Y-%m-%d-%H%M%S')}_{file_interval.upper()}.xlsx"
        return file_name

    def get_data_as_of_text(self, unchanged_tickers, max_date, interval):
        try:
            if unchanged_tickers:
                grouped_tickers = {}
                for ticker, date in unchanged_tickers.items():
                    if date in grouped_tickers:
                        grouped_tickers[date].append(ticker)
                    else:
                        grouped_tickers[date] = [ticker]

                tickers_same_date = {date: tickers for date, tickers in grouped_tickers.items() if len(tickers) > 1}
                tickers_to_place_at_end = [tickers for tickers in tickers_same_date.values()]

                grouped_tickers = [
                    f"{', '.join(tickers)} data is till {date.strftime('%b-%d-%Y')}"
                    for date, tickers in grouped_tickers.items()
                    if tickers not in tickers_to_place_at_end
                ]

                output = grouped_tickers + [
                    f"and {', '.join(value)} data is as of {key.strftime('%b-%d-%Y')}"
                    if grouped_tickers else f"{', '.join(value)} data is till {key.strftime('%b-%d-%Y')}"
                    for key, value in tickers_same_date.items()
                ]

                result = 'NOTE: {}. While the rest is up to date as of {}'.format(', '.join(output),
                                                                                  max_date.strftime('%b-%d-%Y'))
            else:
                if interval == 'weekly':
                    result = f"NOTE: data as of {max_date.strftime('%b-%d-%Y')}"
                else:
                    result = f"NOTE: data as of {max_date.strftime('%b-%d-%Y')}"
            return result
        except Exception as e:
            logger.error(f'All reports sending failed due to error occurred in date fetching. Reason: {str(e)}')
            return

    def fetch_data(self, interval, client, datasets):
        data_dict = {key: dict(data=dict(), msa_data=dict(), msa_dates=dict(), dates=(), ticker_msa_data=dict(),
                               beds_data=dict()) for key, val in reports_mapping.items()}
        for key1, val1 in reports_mapping.items():
            if key1 in datasets:
                dataset = val1[interval]
                for metric in dataset['metric_query'].keys():
                    ticker_query, serializer, metric, unit_col = (
                        dataset['metric_query'][metric]['ticker_query'], dataset['metric_query'][metric]['serializer'],
                        dataset['metric_query'][metric]['metric'],
                        dataset['metric_query'][metric]['unit_col'])

                    if key1 not in ['SENIOR LIVING']:
                        data_dict[key1]['data'][metric] = self.get_interval_data(client, ticker_query,
                                                                                 serializer, metric, unit_col, key1)
                    if key1 in ['SINGLEFAMILY', 'MULTIFAMILY']:
                        data_dict[key1]['msa_data'][metric], data_dict[key1]['msa_dates'][metric] = self.get_msa_data(
                            client, dataset['metric_query'][metric]['msa_query'], key1)
                    elif key1 in ['SHOPPING CENTER']:
                        data_dict[key1]['msa_data'][metric], data_dict[key1]['msa_dates'][metric] = self.get_msa_data(
                            client, dataset['metric_query'][metric]['msa_query'], key1)
                    elif key1 in ['OFFICE']:
                        data_dict[key1]['msa_data'][metric], data_dict[key1]['msa_dates'][metric], \
                        data_dict[key1]['ticker_msa_data'][metric] = \
                            self.get_offices_msa_data(client, dataset['metric_query'][metric]['msa_query'],
                                                      dataset['metric_query'][metric]['ticker_msa_query'], key1,
                                                      interval)
                    elif key1 == 'SELF STORAGE':
                        data_dict[key1]['msa_data'][metric], data_dict[key1]['msa_dates'][metric] \
                            = self.get_msa_data_ss(client, dataset['metric_query'][metric]['msa_query'],
                                                   dataset['metric_query'][metric]['ticker_eff_webyy_data'],
                                                   dataset['metric_query'][metric]['ticker_eff_webyy_dates'],
                                                   dataset['metric_query'][metric]['ticker_eff_webyy_all_reits_data'],
                                                   dataset['distinct_msa'],
                                                   serializer, metric, interval)
                    elif key1 in ['SENIOR LIVING']:
                        data_dict[key1]['msa_data'][metric], data_dict[key1]['msa_dates'][metric] = self.get_msa_data(
                            client, dataset['metric_query'][metric]['msa_query'], key1)
                        data_dict[key1]['data'][metric], data_dict[key1]['beds_data'][metric] = \
                            self.get_interval_data_sl(client, ticker_query,
                                                      dataset['metric_query'][metric]['beds_query'],
                                                      dataset['living_option_beds'],
                                                      dataset['total_tickers_query'],
                                                      serializer, metric, unit_col, key1)
                data_dict[key1]['dates'] = self.get_dates(client, dataset['date_query'])
                data_dict[key1]['unchanged_tickers'], data_dict[key1][
                    'max_analysis_date'] = self.get_outdated_tickers(client, dataset['analysis_date_query'], interval,
                                                                     key1)
        return data_dict

    def get_interval_data(self, client, ticker_query, serializer, metric, unit_col, dataset):
        # same store pool in reports table
        data = client.query(
            ticker_query.format(ticker_query_condition=reports_ticker_query_condition[dataset])).result()
        data = dict(
            serializer(data, many=True, context={'ticker_combine': False, 'single_metric': True}).data)
        self.format_data(data, metric, unit_col, dataset)
        return data

    def get_interval_data_sl(self, client, ticker_query, beds_query, living_option_beds, total_tickers_query,
                             serializer, metric, unit_col, dataset):
        # same store pool in reports table
        data = client.query(
            ticker_query.format(ticker_query_condition=reports_ticker_query_condition[dataset])).result()
        live_mode_beds = [loads(item[0]) for item in client.query(living_option_beds).result()]
        total_tickers = list(
            map(itemgetter('ticker'), [loads(item[0]) for item in client.query(total_tickers_query).result()]))
        beds_data = client.query(beds_query).result()
        data_dict = {
            ticker.lower(): {
                liv_mode.get('living_option'): {
                    bed: dict() for bed in liv_mode.get('beds_array')
                } for liv_mode in live_mode_beds
            } for ticker in total_tickers
        }
        for item in beds_data:
            item = loads(item[0])
            row = {
                f'{calendar.month_abbr[int(item["dated"].split("-")[1])]}-{item["dated"].split("-")[0]}-{item["ticker"].upper()}': {
                    'metric': metric, 'unit_col': unit_col, **item}
            }
            data_dict[item.pop('ticker').lower()][item.pop('living_option')][item.pop('beds')].update(row)
        data = dict(
            serializer(data, many=True, context={'ticker_combine': False, 'single_metric': True,
                                                 'living_option': False, 'tickers': ['atria']}).data)
        self.format_data(data, metric, unit_col, dataset)
        return data, data_dict

    @staticmethod
    def format_data(data, metric, unit_col, dataset):
        if dataset in ['SINGLEFAMILY', 'MULTIFAMILY']:
            for ticker, items in data.items():
                data[ticker] = {
                    f'{calendar.month_abbr[int(item["date"].split("-")[1])]}-{item["date"].split("-")[0]}': {
                        'metric': metric, 'unit_col': unit_col, **item} for item in items}
        elif dataset == 'SHOPPING CENTER':
            for ticker, items in data.items():
                data[ticker] = {
                    f'{calendar.month_abbr[int(item["date"].split("-")[1])]}-{item["date"].split("-")[0]}-{item["unit_area"]}': {
                        'metric': metric, 'unit_col': unit_col, **item} for item in items}
        elif dataset == 'OFFICE':
            for ticker, items in data.items():
                data[ticker] = {
                    f'{calendar.month_abbr[int(item["date"].split("-")[1])]}-{item["date"].split("-")[0]}': {
                        'metric': metric, 'unit_col': unit_col, **item} for item in items}
        elif dataset == 'SELF STORAGE':
            for ticker, items in data.items():
                data[ticker] = {
                    f'{calendar.month_abbr[int(item["date"].split("-")[1])]}-{item["date"].split("-")[0]}-{item["ticker"].upper()}': {
                        'metric': metric, 'unit_col': unit_col, **item} for item in items}
        elif dataset == 'SENIOR LIVING':
            data.pop('min_date')
            data.pop('max_date')
            for ticker, items in data.items():
                all_liv_modes = dict()
                for liv_mode, liv_mode_dict in items.items():
                    all_liv_modes[liv_mode] = {
                        f'{calendar.month_abbr[int(item["date"].split("-")[1])]}-{item["date"].split("-")[0]}-{item["ticker"].upper()}': {
                            'metric': metric, 'unit_col': unit_col, **item} for item in liv_mode_dict
                    }
                data[ticker] = all_liv_modes

    @staticmethod
    def get_dates(client, query):
        dates = natsort.natsorted([loads(item[0])['date'] for item in client.query(query).result()])
        return [f'{calendar.month_abbr[int(_date.split("-")[1])]}-{_date.split("-")[0]}' for _date in dates]

    @staticmethod
    def get_outdated_tickers(client, date_query, interval, dataset):
        ticker_dates = {data['ticker']: datetime.strptime(data['analysis_date'], '%Y-%m-%d').date() for data in
                        [loads(item[0]) for item in client.query(
                            date_query.format(
                                ticker_query_condition=reports_ticker_query_condition[dataset])).result()]}
        tickers_not_updated = dict()
        maximum_date = datetime.now()
        if interval == 'monthly':
            maximum_date = date.today() - timedelta(days=date.today().day)
        if interval == 'weekly':
            maximum_date = max(ticker_dates.values())

        for ticker in ticker_dates.keys():
            if ticker_dates[ticker] < maximum_date:
                tickers_not_updated[ticker] = ticker_dates[ticker]
        return tickers_not_updated, maximum_date

    @staticmethod
    def get_msa_data(client, query, dataset):
        msa_list = REPORT_MSAs['IMPROVED_MSAs'][dataset]
        if dataset in ['SINGLEFAMILY', 'MULTIFAMILY']:
            result = [loads(item[0]) for item in client.query(
                query.format(ticker_query_condition=reports_ticker_query_condition[dataset],
                             improved_msa_list=tuple(msa_list))).result()]
            item = next(iter(result), {})
            msa_dates = {'cur_year': item.get('cur_year'), 'prev_year': item.get('prev_year')}
        elif dataset in ['SELF STORAGE']:
            result = [loads(item[0]) for item in client.query(query.format(
                ticker_query_condition=reports_ticker_query_condition[dataset],
                improved_msa_list=tuple(msa_list)
            )).result()]
            item = next(iter(result), {})
            msa_dates = {'cur_month': item.get('cur_month'), 'prev_month': item.get('prev_month')}
        elif dataset in ['SHOPPING CENTER']:
            result = [loads(item[0]) for item in client.query(query.format(
                ticker_query_condition=reports_ticker_query_condition[dataset],
                improved_msa_list=tuple(msa_list)
            )).result()]
            item = next(iter(result), {})
            msa_dates = {'cur_month': item.get('cur_month'), 'prev_month': item.get('prev_month'),
                         'prev_prev_month': item.get('prev_prev_month')}
        elif dataset in ['SENIOR LIVING']:
            live_modes = ['Assisted/Supportive', 'Memory Care', 'Independent']
            result = list()
            all_living_options = dict()
            for liv_mode in live_modes:
                filters = {'living_option': liv_mode, 'improved_msa_list': tuple(msa_list[liv_mode]),
                           'ticker_query_condition': reports_ticker_query_condition[dataset]}
                liv_mode_result = [loads(item[0]) for item in client.query(query.format(**filters)).result()]
                all_living_options[liv_mode] = liv_mode_result
                item = next(iter(liv_mode_result), {})
                msa_dates = {'cur_month': item.get('cur_month'), 'prev_month': item.get('prev_month'),
                             'prev_prev_month': item.get('prev_prev_month')}
            result.append(all_living_options)
        return result, msa_dates

    @staticmethod
    def get_offices_msa_data(client, msa_query, ticker_query, dataset, interval):
        cur_month = '{}-{}'.format(datetime.now().year, datetime.now().month)
        ticker_condition = reports_ticker_query_condition[dataset]
        msa_list = REPORT_MSAs['IMPROVED_MSAs'][dataset]
        ticker_msa_list = REPORT_MSAs['TICKER_WISE_MSAs']['vts']
        if interval == 'weekly':
            result = [loads(item[0]) for item in
                      client.query(msa_query.format(improved_msa_list=tuple(msa_list),
                                                    ticker_query_condition=ticker_condition)).result()]
            ticker_result = [loads(item[0]) for item in
                             client.query(ticker_query.format(improved_msa_list=tuple(ticker_msa_list),
                                                              ticker_query_condition=ticker_condition)).result()]
            if not result:
                prev_month = datetime.now() - timedelta(days=datetime.now().day)
                cur_month = '{}-{}'.format(datetime.now().year, prev_month.month)
                result = [loads(item[0]) for item in
                          client.query(msa_query.format(improved_msa_list=tuple(msa_list),
                                                 ticker_query_condition=ticker_condition)).result()]
                ticker_result = [loads(item[0]) for item in
                                 client.query(ticker_query.format(tuple(ticker_msa_list), cur_month)).result()]
        else:
            result = [loads(item[0]) for item in client.query(msa_query.format(tuple(msa_list), cur_month)).result()]
            ticker_result = [loads(item[0]) for item in
                             client.query(ticker_query.format(tuple(ticker_msa_list), cur_month)).result()]

        item = next(iter(result), {})
        msa_dates = {'cur_month': item.get('cur_month'), 'prev_month': item.get('prev_month'),
                     'prev_prev_month': item.get('prev_prev_month')}
        return result, msa_dates, ticker_result

    def get_msa_data_ss(self, client, query, ticker_web, ticker_web_dates, reits_web, distinct_msa, serializer, metric,
                        interval):
        msa_list = REPORT_MSAs['IMPROVED_MSAs']['SELFSTORAGE']
        if interval == 'weekly':
            msa_result = [loads(item[0]) for item in client.query(query.format(
                ticker_query_condition=reports_ticker_query_condition["SELF STORAGE"],
                improved_msa_list=tuple(msa_list))).result()]
        else:
            msa_result = [loads(item[0]) for item in client.query(query.format(tuple(msa_list))).result()]
        distinct_msa = {key: [{'msa': msa.replace('"', '')} for msa in value] for key, value in
                        REPORT_MSAs['TICKER_WISE_MSAs'].items()}
        item = next(iter(msa_result), {})
        msa_date = {'cur_month': item.get('cur_month'), 'prev_month': item.get('prev_month')}
        ticker_web_result = client.query(ticker_web.format(
            ticker_query_condition=reports_ticker_query_condition["SELF STORAGE"])).result()
        reits_web_result = client.query(reits_web).result()
        ticker_web_result = dict(
            serializer(ticker_web_result, many=True, context={'ticker_combine': False, 'single_metric': True}).data)
        reits_web_result = dict(
            serializer(reits_web_result, many=True, context={'ticker_combine': False, 'single_metric': True}).data)
        filtered_ticker_web_result = {}
        for web_ticker, web_items in reits_web_result.items():
            filtered_data_items = []
            for web_item in web_items:
                for ticker_msa in REPORT_MSAs['TICKER_WISE_MSAs']['ALL REITs']:
                    if ticker_msa == web_item['msa']:
                        filtered_data_items.append(web_item)
            filtered_ticker_web_result['ALL REITs'] = filtered_data_items

        for web_ticker, web_items in ticker_web_result.items():
            if web_ticker in REPORT_MSAs['TICKER_WISE_MSAs'].keys():
                filtered_data_items = []

                for web_item in web_items:
                    for ticker_msa in REPORT_MSAs['TICKER_WISE_MSAs'][web_ticker]:
                        if ticker_msa == web_item['msa']:
                            filtered_data_items.append(web_item)
                filtered_ticker_web_result[web_ticker] = filtered_data_items
        for ticker, items in filtered_ticker_web_result.items():
            filtered_ticker_web_result[ticker] = {
                f'{calendar.month_abbr[int(item["date"].split("-")[1])]}-{item["date"].split("-")[0]}-{item["msa"]}': {
                    'metric': metric, **item} for item in items}
        ticker_web_date = self.get_dates(client,
                                         ticker_web_dates.format(
                                             ticker_query_condition=reports_ticker_query_condition["SELF STORAGE"]))
        msa_dates = {'msa_dates': msa_date, 'ticker_web_date': ticker_web_date}
        result = {'result': msa_result, 'ticker_web_result': filtered_ticker_web_result, 'distinct_msa': distinct_msa}
        return result, msa_dates

    @staticmethod
    def render_to_pdf(html):
        result = io.BytesIO()
        try:
            pdf = pisa.pisaDocument(io.BytesIO(html.encode("utf-8")), result)
        except Exception:
            return False, None
        if pdf.err:
            return False, None
        return True, result.getvalue()

    @staticmethod
    def save_data_to_workbook(query_mapping, client, workbook_data, dataset, interval):
        workbook = openpyxl.Workbook()

        for sheet_name, query in query_mapping[dataset][interval].items():
            sheet = workbook.create_sheet(title=sheet_name)
            if sheet_name == "Default Portfolio Years":
                sheet.title = 'Default Portfolio Years'
                sheet.append(["Ticker", "Default Portfolio Years"])
                for ticker, years in ticker_default_pf[dataset].items():
                    sheet.append([ticker, "-".join(map(str, years))])
            else:
                dataset_ = dataset.replace(" ", "") if dataset == "SELF STORAGE" else dataset
                if sheet_name == "Ticker-Wise Improved MSAs":
                    msa_list_ = REPORT_MSAs['TICKER_WISE_MSAs']
                    msa_list = "OR ".join((f"(ticker='{t.upper()}' and msa in {tuple(msa)})"
                                           for t, msa in msa_list_.items() if t != "ALL REITs"))
                elif sheet_name == "Improved MSAs (PVT)":
                    msa_list_ = REPORT_MSAs['TICKER_WISE_MSAs']['vts']
                    msa_list = tuple(msa_list_)
                else:
                    msa_list = tuple(REPORT_MSAs['IMPROVED_MSAs'][dataset_])
                    if dataset == "SENIOR LIVING" and sheet_name.endswith("Improved MSAs"):
                        living_option = {
                            'Assisted Improved MSAs': 'Assisted/Supportive',
                            'Independent Improved MSAs': 'Independent',
                            'Memory Care Improved MSAs': 'Memory Care',
                        }
                        msa_list = tuple(REPORT_MSAs['IMPROVED_MSAs'][dataset_][living_option[sheet_name]])
                data_rows = client.query(
                    query.format(ticker_query_condition=reports_ticker_query_condition[dataset],
                                 improved_msa_list=msa_list)).result()
                headers_written = False
                for row in data_rows:
                    values = list(loads(row[0]).values())
                    if not headers_written:
                        headers = list(loads(row[0]).keys())
                        sheet.append(headers)
                        headers_written = True
                    sheet.append(values)

        workbook.remove(workbook['Sheet'])
        workbook.save(workbook_data)
        workbook_data.seek(0)


class SendApprovedReports(APIView):
    permission_classes = [IsAdmin]
    queryset = SubscribeReports.objects.all()
    serializer_class = SendApprovedReportsSerializer
    gcp_project = 'webdata-207211'

    def post(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        interval = serializer.validated_data['interval']
        all_subscriber = serializer.validated_data['all_subscriber']
        datasets = request.data.get('datasets')
        send_to = request.data.get('send_to')
        if interval not in ('weekly', 'monthly'):
            raise GenericAPIException('invalid interval')
        try:
            if all_subscriber:
                file_name = self.get_report_name(interval)
                self.reports_status_update(datasets, file_name, {'status': 'In Progress'})
        except Exception as e:
            raise GenericAPIException(str(e))

        SendReportsThread(view=self, interval=interval, all_subscriber=all_subscriber,
                          datasets=datasets, send_to=send_to).start()

        # with ThreadPoolExecutor() as executor:
        #     future = executor.submit(self.send_reports, interval, all_subscriber, datasets, send_to)
        # try:
        #     # Block until the future is completed
        #     result = future.result()
        #     logger.info(f'Report process completed successfully for interval: {interval}')
        # except Exception as e:
        #     # Log any exceptions that occur during the asynchronous task
        #     logger.error(f'Error sending reports: {str(e)}')
        #     raise GenericAPIException(f"Failed to send reports: {str(e)}")

        return Response(status=status.HTTP_200_OK)

    def send_reports(self, interval, all_subscriber, datasets=None, send_to=None):
        pdfs = dict()
        xlsx = dict()
        file_name = self.get_report_name(interval)
        required_datasets = [d for d in datasets if d in reports_active_datasets.keys()] if datasets else \
            list(reports_active_datasets.keys())
        for dataset in required_datasets:
            if UserReportDetails.objects.filter(report_name=file_name, dataset=dataset, approved=True).exists():
                report = UserReportDetails.objects.filter(report_name=file_name, dataset=dataset, interval=interval,
                                                          approved=True).values('file_path', 'xlsx_file_path',
                                                                                'sent_at').order_by(
                    '-published_at').first()
                self.get_reports_to_send(report, dataset, interval, pdfs, xlsx, all_subscriber)
        if len(pdfs) == 0:
            logger.info('Approval is pending for the reports.')

        kwargs = {interval: True, 'snooze': False}
        if datasets:
            users = self.queryset.filter(**kwargs).values('user').annotate(datasets=GroupConcat('dataset')).filter(
                Q(dataset__in=datasets))
        else:
            users = self.queryset.filter(**kwargs).values('user').annotate(datasets=GroupConcat('dataset'))
        users_with_datasets = self.get_users(users, send_to, datasets, all_subscriber)
        if users_with_datasets:
            receiver_details = dict()
            attached_file = 0
            for user_id, datasets_list in users_with_datasets.items():
                active_user_email = list(
                    User.objects.filter(pk=user_id, is_active=True).values('email')) if all_subscriber else False
                user_email = user_id if not all_subscriber else (
                    active_user_email[0].get('email') if active_user_email else False)
                if user_email:
                    receiver_details[user_email] = dict()
                    receiver_details_datasets = list()
                    email = EmailMultiAlternatives(f'{interval.capitalize()} Research Reports',
                                                   'Please find attached reports.',
                                                   settings.EMAIL_HOST_USER, [user_email])
                    for dataset in datasets_list:
                        if dataset in pdfs.keys():
                            email.attach(f'{dataset}_{interval.upper()}_REPORT.pdf', pdfs[dataset], 'application/pdf')
                            email.attach('{}.xlsx'.format(dataset), xlsx[dataset],
                                         'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                            attached_file += 1
                            receiver_details_datasets.append(dataset)
                    if attached_file:
                        try:
                            email.send()
                            logger.info(f'reports sent to {user_email}')
                            receiver_details[user_email]['send_at'] = datetime.now().strftime('%Y-%m-%d %H:%M')
                            receiver_details[user_email]['datasets'] = ' , '.join(receiver_details_datasets)
                        except Exception as e:
                            logger.error(
                                f'report(s) sending failed to email address: {user_email}. Reason: {str(e)}')
                            receiver_details[user_email][
                                'send_at'] = f'Failed at {datetime.now().strftime("%Y-%m-%d %H:%M")}'
                            receiver_details[user_email]['error'] = f'Reason: {str(e)}'
                            receiver_details[user_email]['datasets'] = ' , '.join(receiver_details_datasets)
                else:
                    logger.info(
                        f'Report(s) sending failed to user: {user_id}. Reason: Email not found or user is inactive')
            context_data = dict()
            context_data['data'] = receiver_details
            send_email_to_user(to_email=settings.REPORT_DETAILS_EMAIL.split(','),
                               data=context_data, _for='reports_receiver_details')
            if all_subscriber:
                self.reports_status_update(users_with_datasets, file_name, {'sent_at': datetime.now(),
                                                                            'status': 'Sent'})

    def reports_status_update(self, users_with_datasets, file_name, column_value):
        datasets_report_sent = list()
        if isinstance(users_with_datasets, dict):
            users_with_datasets = set(list(itertools.chain.from_iterable(users_with_datasets.values())))
        for dataset_ in users_with_datasets:
            if UserReportDetails.objects.filter(report_name=file_name, dataset=dataset_,
                                                approved=True).exists():
                datasets_report_sent.append(dataset_)
        sent_reports = datasets_report_sent
        UserReportDetails.objects.filter(dataset__in=sent_reports, report_name=file_name).update(**column_value)

    def get_reports_to_send(self, report, dataset, interval, pdfs, xlsx, all_subscriber):
        if report['sent_at'] and all_subscriber:
            if interval == "weekly" and (timezone.now() - timedelta(days=5)) > report['sent_at']:
                pdfs[dataset] = self.get_pdf_from_bucket(report['file_path'])
                xlsx[dataset] = self.get_pdf_from_bucket(report['xlsx_file_path'])
            elif interval == "monthly" and report['sent_at'].month == datetime.today().month - 1:
                pdfs[dataset] = self.get_pdf_from_bucket(report['file_path'])
                xlsx[dataset] = self.get_pdf_from_bucket(report['xlsx_file_path'])
            else:
                logger.info(f'{dataset} report sent recently.')
        else:
            pdfs[dataset] = self.get_pdf_from_bucket(report['file_path'])
            xlsx[dataset] = self.get_pdf_from_bucket(report['xlsx_file_path'])

    def get_users(self, users, send_to, datasets, all_subscriber):
        user_ids, req_datasets = send_to, datasets
        users_and_datasets = {item: req_datasets for item in user_ids} if user_ids and req_datasets else None
        if all_subscriber:
            users_with_datasets = {item['user']: item['datasets'].split(',') for item in users}
        else:
            users_with_datasets = users_and_datasets
        return users_with_datasets

    def get_report_name(self, interval):
        target_date = date.today()
        iso_year, iso_month, iso_week = self.get_majority_month_of_iso_week(target_date)

        if interval == 'monthly':
            return f"{iso_month}-{iso_year}"
        elif interval == 'weekly':
            return f"{iso_month}-{iso_week}-week-{iso_year}"

    def get_majority_month_of_iso_week(self, target_date):
        iso_year, iso_week, iso_weekday = target_date.isocalendar()
        # Get the start of the ISO week (Monday)
        start_of_week = target_date - timedelta(days=iso_weekday)
        # Collect all 7 days of the ISO week
        week_days = [start_of_week + timedelta(days=i) for i in range(7)]
        # Count the occurrences of each month in the week
        month_counts = {}
        for day in week_days:
            month_counts[day.month] = month_counts.get(day.month, 0) + 1
        # Find the month with the most days in the week
        majority_month = max(month_counts, key=month_counts.get)
        majority_month_name = target_date.replace(month=majority_month).strftime("%B")
        return iso_year, majority_month_name, iso_week

    @staticmethod
    def save_data_to_workbook(query_mapping, client, workbook_data, dataset, interval):
        workbook = openpyxl.Workbook()

        for sheet_name, query in query_mapping[dataset][interval].items():
            sheet = workbook.create_sheet(title=sheet_name)
            data_rows = client.query(query).result()

            data_rows_list = list(data_rows)
            headers = list(loads(data_rows_list[0][0]).keys())
            sheet.append(headers)

            for row in data_rows_list:
                values = list(loads(row[0]).values())
                sheet.append(values)

        workbook.remove(workbook['Sheet'])
        workbook.save(workbook_data)
        workbook_data.seek(0)

    def get_pdf_from_bucket(self, file_name):
        client = storage.Client(project=settings.DOWNLOAD_REPORTS_PROJ)
        bucket = client.bucket(settings.GS_REPORT_BUCKET_NAME)
        blob = bucket.blob(file_name)
        try:
            pdf_content = blob.download_as_bytes()
            return pdf_content
        except Exception as e:
            print(f"Error retrieving {file_name} PDF: {e}")
            return None


class GenerateReports(APIView):
    permission_classes = [IsAdmin]
    gcp_project = 'webdata-207211'
    send_reports = SendReports()

    def post(self, request):
        interval = request.query_params.get('interval')
        datasets = request.data.get('datasets')
        send_to = request.data.get('send_to')
        if interval not in ('weekly', 'monthly'):
            raise GenericAPIException('invalid interval')
        GenerateReportsThread(view=self, interval=interval, datasets=datasets, send_to=send_to).start()
        return Response(status=status.HTTP_200_OK)

    def generate_reports(self, interval, datasets=None, send_to=None):
        pdfs = dict()
        xlsx = dict()
        datasets = set(dataset for dataset in reports_active_datasets.keys()) if not datasets else set(datasets)
        client = bigquery.Client(project=self.gcp_project)
        try:
            all_data = self.send_reports.fetch_data(interval, client, datasets)
        except Exception as e:
            logger.error(f'Reports creation failed due to error occurred in data fetching. Reason: {str(e)}')
            return
        for dataset in datasets:
            i = 0
            html_page = ""
            context, dates, msa_data, msa_dates = (
                all_data[dataset]['data'], all_data[dataset]['dates'], all_data[dataset]['msa_data'],
                all_data[dataset]['msa_dates'])

            unchanged_tickers, max_analysis_date = (
                all_data[dataset]['unchanged_tickers'], all_data[dataset]['max_analysis_date'])
            outdated_tickers_list = [ticker for ticker in unchanged_tickers.keys()]
            data_as_of_text = self.send_reports.get_data_as_of_text(unchanged_tickers, max_analysis_date, interval)

            report_header = report_headers[dataset][interval]
            for metric in all_data[dataset]['data'].keys():
                i = i + 1
                if context:
                    if dataset == 'SELF STORAGE':
                        context_data = {
                            'data': context[metric], 'dataset': dataset, 'interval': interval,
                            'metric': metric,
                            'dates': dates,
                            'created_date': max_analysis_date,
                            'published_date': datetime.today().date(),
                            'data_as_of_text': data_as_of_text,
                            'footer_text': reports_footer_text[dataset],
                            'outdated_tickers_list': outdated_tickers_list,
                            'report_header': report_header,
                            'improved_msa': msa_data[metric]['result'],
                            'distinct_msa': msa_data[metric]['distinct_msa'],
                            'ticker_web_result': msa_data[metric]['ticker_web_result'],
                            'msa_dates': msa_dates[metric]['msa_dates'],
                            'ticker_web_date': msa_dates[metric]['ticker_web_date'],
                            'ss_metrics': ss_metrics,
                            'count': i
                        }
                    elif dataset == 'SENIOR LIVING':
                        context_data = {
                            'data': context[metric], 'dataset': dataset, 'interval': interval,
                            'metric': metric, 'dates': dates,
                            'created_date': max_analysis_date,
                            'published_date': datetime.today().date(),
                            'data_as_of_text': data_as_of_text,
                            'footer_text': reports_footer_text[dataset],
                            'outdated_tickers_list': outdated_tickers_list,
                            'report_header': report_header,
                            'improved_msa': msa_data[metric],
                            'msa_dates': msa_dates[metric],
                            'beds_data': all_data[dataset]['beds_data'][metric],
                            'count': i
                        }
                    else:
                        context_data = {
                            'data': context[metric], 'dataset': dataset, 'interval': interval,
                            'metric': metric,
                            'dates': dates,
                            'created_date': max_analysis_date,
                            'published_date': datetime.today().date(),
                            'data_as_of_text': data_as_of_text,
                            'footer_text': reports_footer_text[dataset],
                            'outdated_tickers_list': outdated_tickers_list,
                            'report_header': report_header,
                            'improved_msa': msa_data[metric],
                            'msa_dates': msa_dates[metric],
                            'count': i
                        }
                        if dataset == 'OFFICE':
                            context_data['ticker_improved_msa'] = all_data[dataset]['ticker_msa_data'][metric]
                    if dataset == 'SHOPPING CENTER':
                        context_data['unit_area'] = unit_area
                    template = self.send_reports.map_template(dataset)
                    metric_page = render_to_string(template, context=context_data)
                    if metric_page:
                        html_page += '\n' + metric_page
            if dataset in ['SINGLEFAMILY', 'MULTIFAMILY']:
                disclaimer_template = render_to_string("disclaimer_page.html")
                html_page += '\n' + disclaimer_template
            converted, pdf = self.send_reports.render_to_pdf(html_page)
            self.send_reports.save_report_files_url(dataset, interval, pdf, datetime.today().now(), file_type="pdf")
            workbook_data = io.BytesIO()
            try:
                self.send_reports.save_data_to_workbook(xls_mapping, client, workbook_data, dataset, interval)
                xlsx[dataset] = workbook_data.getvalue()
                self.send_reports.save_report_files_url(dataset, interval, workbook_data.getvalue(),
                                                        datetime.today().now(), file_type="xlsx")
            except Exception as e:
                logger.error(
                    f'xlsx data insertion failed. Reason: {str(e)}')
            if converted:
                pdfs[dataset] = pdf
        if send_to:
            email = EmailMultiAlternatives(f'{interval.capitalize()} Research Reports', 'Please find attached reports.',
                                           settings.EMAIL_HOST_USER, [send_to])
            if pdfs:
                attached_file = 0
                for dataset in pdfs.keys():
                    email.attach(f'{dataset}_{interval.upper()}_REPORT.pdf', pdfs[dataset], 'application/pdf')
                    if dataset in xlsx.keys():
                        email.attach(f'{dataset}.xlsx', xlsx[dataset],
                                     'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                    attached_file += 1
                if attached_file:
                    try:
                        email.send()
                    except Exception as e:
                        logger.error(f'report(s) sending failed to email address: {send_to}. Reason: {str(e)}')


class ViewReports(GenericViewSet, UpdateModelMixin, ListModelMixin, DestroyModelMixin):
    permission_classes = [IsAdmin]
    queryset = UserReportDetails.objects.filter(
        Q(xlsx_file_path__isnull=False) & (Q(approved=False) | Q(sent_at__isnull=True)))
    serializer_class = UserReportDetailsSerializer

    def put(self, request):
        id_list = request.data.get('ids', [])
        approved = request.data.get('approved', False)
        if len(id_list) > 0:
            try:
                UserReportDetails.objects.filter(id__in=id_list).update(approved=approved)
            except Exception as e:
                return Response(str(e), status=status.HTTP_400_BAD_REQUEST)
            return Response(status=status.HTTP_200_OK)
        else:
            return Response(data={'details': 'no report selected'}, status=status.HTTP_200_OK)


class DatasetRequestEmail(APIView):
    http_method_names = ['post']
    permission_classes = [IsAuth]
    serializer_class = DatasetPermRequestSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data, context={'request': self.request})
        serializer.is_valid(raise_exception=True)
        data = serializer.data
        email_already_send = (UserDatasetPermRequest.objects
                              .filter(user_id=self.request.user.id, dataset=data['dataset'],
                                      created_time__gt=(date.today() - dt.timedelta(days=3))))
        UserDatasetPerm = UserDatasetPermRequest(**data, user_id=self.request.user.id)
        UserDatasetPerm.save()
        context = {'message': data['message'], 'permission_panel': settings.ADMIN_PERM_PANEL}
        if not email_already_send:
            send_email_to_user(to_email=[settings.DATASET_PERM_REQUEST], data=context, _for='request_dataset_perm')
        response = [
            f'We have received your request to subscribe to our {data["dataset"]} dataset. A representative will '
            f'contact you shortly with access information.']
        return Response(data=response, status=status.HTTP_200_OK)


class ResetPasswordViewSet(viewsets.GenericViewSet):
    serializer_class = djoser_settings.SERIALIZERS.user
    queryset = User.objects.all()
    permission_classes = (AllowAny,)
    token_generator = default_token_generator
    lookup_field = djoser_settings.USER_ID_FIELD

    @action(["post"], detail=False)
    def reset_password(self, request, *args, **kwargs):
        serializer = SendEmailResetSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.get_user()

        if user:
            context = {"user": user}
            to = [get_user_email(user)]
            try:
                djoser_settings.EMAIL.password_reset(self.request, context).send(to)
            except TimeoutError:
                raise GenericAPIException('request cannot be processed at this time. Please try later')
        return Response(status=status.HTTP_200_OK)

    @action(["post"], detail=False)
    def reset_password_confirm(self, request, *args, **kwargs):
        serializer = PasswordResetConfirmRetypeSerializer(data=request.data,
                                                          context={'request': request, 'view': self})
        serializer.is_valid(raise_exception=True)

        serializer.user.set_password(serializer.data["new_password"])
        if hasattr(serializer.user, "last_login"):
            serializer.user.last_login = now()
        serializer.user.save()
        if serializer.user.is_bbi_user:
            bbi_user = BBIWebAuthUser.objects.using('bbi_web').get(username=serializer.user.username)
            bbi_user.password = make_password(serializer.data["new_password"])
            if hasattr(serializer.user, "last_login"):
                bbi_user.last_login = now()
            bbi_user.save(using='bbi_web')

        if djoser_settings.PASSWORD_CHANGED_EMAIL_CONFIRMATION:
            context = {"user": serializer.user}
            to = [get_user_email(serializer.user)]
            try:
                djoser_settings.EMAIL.password_changed_confirmation(self.request, context).send(to)
            except TimeoutError:
                pass
        return Response(status=status.HTTP_200_OK)


class DownloadReports(APIView):
    http_method_names = ['post', 'get']
    permission_classes = [IsAuth]
    queryset = UserReportDetails.objects.filter(~Q(sent_at__isnull=True) & ~Q(approved=False))

    def get_user_perm(self, datasets, request):
        if request.user.is_staff or request.user.is_superuser:
            return True
        if isinstance(datasets, list):
            permission_name = [DATASET_PERMS[dataset]['permissions']['Report Download']['name'] for dataset in datasets]
            try:
                permission = list(Permission.objects.filter(name__in=permission_name))
            except ObjectDoesNotExist:
                raise GenericAPIException(f"The permission '{permission_name}' does not exist")
            assigned_perm = CustomUserPerm.objects.filter(user=request.user.id, permission__in=permission).count()
            if assigned_perm == len(permission):
                return True
            else:
                return False
        if isinstance(datasets, str):
            permission_name = DATASET_PERMS[datasets]['permissions']['Report Download']['name']
            try:
                permission = Permission.objects.get(name=permission_name)
            except ObjectDoesNotExist:
                raise GenericAPIException(f"The permission '{permission_name}' does not exist")
            return CustomUserPerm.objects.filter(user=request.user.id, permission=permission).exists()

    def post(self, request):
        arguments = ValidateReportsArguments(data=request.data)
        arguments.is_valid(raise_exception=True)
        arguments = arguments.validated_data
        datasets = arguments.get('dataset')
        interval = arguments.get('timeframe')
        years = arguments.get('years')
        sort_data = self.request.query_params.get('published_at', 'desc')
        response = dict()
        dataset_perm = self.get_user_perm(datasets, request)
        if dataset_perm and interval and years:
            try:
                if sort_data == 'asc':
                    queryset = self.queryset.filter(dataset__in=datasets, interval=interval,
                                                    report_year__in=years).values(
                        'report_name', 'dataset', 'published_at', 'report_year').order_by('published_at')
                else:
                    queryset = self.queryset.filter(dataset__in=datasets, interval=interval,
                                                    report_year__in=years).values(
                        'report_name', 'dataset', 'published_at', 'report_year').order_by('-published_at')
                page_size = int(request.query_params.get("page_size", 25)) if int(
                    request.query_params.get("page_size", 25)) in range(
                    1, 101) else 25
                paginator = Paginator(queryset, page_size)
                page_number = int(request.query_params.get("page", 1))
                total_pages = paginator.num_pages
                if page_number <= total_pages:
                    page_obj = paginator.get_page(page_number)
                    response = {"page_count": total_pages, "data": page_obj.object_list}
                else:
                    raise GenericAPIException(f"page not found")
            except Exception as e:
                raise GenericAPIException(f"{str(e)}")
        else:
            raise GenericAPIException(f"You don't have permission to download reports of {datasets}")
        return Response(data=response, status=status.HTTP_200_OK)

    def get(self, request):
        column_name = None
        content_type = None
        file_extension = None
        report_name = self.request.query_params.get('report_name')
        report_type = self.request.query_params.get('type')
        if report_type == "pdf":
            column_name = 'file_path'
            content_type = 'application/pdf'
            file_extension = 'pdf'
        elif report_type == "xlsx":
            column_name = 'xlsx_file_path'
            content_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            file_extension = 'xlsx'
        if not report_name:
            raise GenericAPIException(f"Please send a report name in params to download")
        dataset = self.request.query_params.get('dataset')
        if dataset not in active_datasets.values():
            raise GenericAPIException(f"Please send a valid dataset in params i.e {list(active_datasets.values())}")
        dataset = dataset.upper().replace('_', ' ')
        dataset_perm = self.get_user_perm(dataset, request)
        if report_name and dataset_perm:
            try:
                report_name = UserReportDetails.objects.filter(dataset=dataset, report_name=report_name).values(
                    column_name).first()
                if report_name:
                    file_path = report_name.get(column_name)
                    client = storage.Client(project=settings.DOWNLOAD_REPORTS_PROJ)
                    bucket = client.bucket(settings.GS_REPORT_BUCKET_NAME)
                    blob = bucket.blob(file_path)
                    file_content = blob.download_as_bytes()
                    file_wrapper = io.BytesIO(file_content)
                    response = HttpResponse(file_wrapper, content_type=content_type)
                    response['Content-Disposition'] = f'attachment; filename="file.{file_extension}"'
                    return response
                else:
                    raise GenericAPIException(f"Please send a valid report_name in params ")
            except Exception as e:
                logger.error(f'report(s) downloading failed. Reason: {str(e)}')
                raise GenericAPIException(f"unable to download reports")
        else:
            raise GenericAPIException(f"You don't have permission to download reports of {dataset}")


class UserSubscribedReportsDetails(APIView):
    http_method_names = ['post', 'get']
    permission_classes = [IsAdmin]
    queryset = SubscribeReports.objects.all()

    def post(self, request):
        user_id = request.query_params.get('users')
        queryset = self.queryset.filter(Q(weekly=True) | Q(monthly=True)).select_related('user')
        if request.query_params.get('show_clients'):
            queryset = queryset.filter(Q(user__is_client=self.refactor_param_val(self.request.query_params.get(
                'show_clients').lower()))).select_related('user')
        if request.query_params.get('is_active'):
            queryset = queryset.filter(Q(user__is_client=self.refactor_param_val(self.request.query_params.get(
                'is_active').lower()))).select_related('user')

        result = dict()
        if user_id:
            queryset = [item for item in queryset if item.user.id == int(user_id)]
        for item in queryset:
            if item.user.username not in result:
                result[item.user.username] = {'id': item.user.id, 'user': item.user.username, 'email': item.user.email}
            if item.dataset not in result[item.user.username]:
                result[item.user.username][item.dataset] = {'weekly': item.weekly, 'monthly': item.monthly}
        result = list(result.values())
        page_size = int(request.query_params.get("page_size", 25)) if int(
            request.query_params.get("page_size", 25)) in range(
            1, 101) else 25
        paginator = Paginator(result, page_size)
        page_number = int(request.query_params.get("page", 1))
        total_pages = paginator.num_pages
        if page_number <= total_pages:
            page_obj = paginator.get_page(page_number)
            response = {"page_count": total_pages, "data": page_obj.object_list}
        else:
            raise GenericAPIException(f"page not found")
        return Response(data=response, status=status.HTTP_200_OK)

    def get_users(self, listed_user=None):
        queryset = self.queryset.filter(Q(weekly=True) | Q(monthly=True)).select_related('user')
        if listed_user:
            queryset = queryset.filter(Q(user__username__icontains=listed_user))
        result = dict()
        for item in queryset:
            if item.user.username not in result:
                result[item.user.username] = {'id': item.user.id, 'user': item.user.username, 'email': item.user.email}
        return list(result.values())

    def get(self, request):
        if request.query_params.get('listed') and request.query_params.get('listed').lower() == 'true':
            if request.query_params.get('listed_user'):
                listed_user = request.query_params.get('listed_user')
                data = self.get_users(listed_user)
            else:
                data = self.get_users()
            return Response(data)

    @staticmethod
    def refactor_param_val(param):
        key_value = {'true': True, 'false': False}
        return key_value.get(param)


def get_user_totp_device(self, user, confirmed=None):
    devices = devices_for_user(user, confirmed=confirmed)
    for device in devices:
        if isinstance(device, TOTPDevice):
            return device


class TOTPCreateView(views.APIView):
    """
    Use this endpoint to set up a new TOTP device
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, format=None):
        user = request.user
        device = get_user_totp_device(self, user)
        if not device:
            device = user.totpdevice_set.create(confirmed=False)
            # user.otp_device = True
            # user.save()
        url = device.config_url
        return Response(url, status=status.HTTP_201_CREATED)


class TOTPVerifyView(views.APIView):
    """
    Use this endpoint to verify/enable a TOTP device
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_block_time(self, lock_untill):
        seconds = (lock_untill - timezone.now()).seconds
        minutes = seconds // 60
        remaining_seconds = seconds % 60
        time_str = f"{minutes} minute{'s' if minutes != 1 else ''} {remaining_seconds} second{'s' if remaining_seconds != 1 else ''}"
        return time_str

    def post(self, request, token, format=None):
        user = request.user
        device = get_user_totp_device(self, user)
        if not device == None and device.verify_token(token):
            if not device.confirmed:
                device.confirmed = True
                device.save()
                user.otp_device = True
                user.save()
            refresh_token = RefreshToken.for_user(user)
            token = get_custom_otp_jwt(refresh_token, user, device)
            return Response({'refresh': str(refresh_token), 'access': token}, status=status.HTTP_200_OK)
        elif not device == None:
            if device.throttling_failure_count < 4:
                return Response({'message': f'Given token is invalid or expired'}, status=status.HTTP_400_BAD_REQUEST)
            else:
                try:
                    message = device.verify_is_allowed()[1]
                    if message.get('locked_until'):
                        return Response(
                            {
                                'message': f'`Too many attempts. Please try again after {self.get_block_time(message["locked_until"])} .'},
                            status=status.HTTP_400_BAD_REQUEST)
                except:
                    return Response({'message': f'Given token is invalid or expired'},
                                    status=status.HTTP_400_BAD_REQUEST)
        return Response(status=status.HTTP_400_BAD_REQUEST)


class TOTPDeleteView(views.APIView):
    """
    Use this endpoint to delete a TOTP device
    """
    permission_classes = (IsOtpVerified,)

    def post(self, request, format=None):
        user = request.user
        password = request.data.get('password')
        if not user.check_password(password):
            return Response({'error': 'Invalid password'}, status=status.HTTP_400_BAD_REQUEST)
        devices = devices_for_user(user, confirmed=None)
        if not any(devices):
            return Response({'message': f'No TOTP device found for user {user.username}'},
                            status=status.HTTP_400_BAD_REQUEST)
        devices = devices_for_user(user, confirmed=None)
        for device in devices:
            device.delete()
            user.otp_device = False
            user.save()
        user.jwt_secret = uuid.uuid4()
        user.save()
        token = get_custom_jwt(user, None)
        return Response({'message': f'TOTP devices deleted for user {user.username}', 'token': token},
                        status=status.HTTP_200_OK)


class TOTPDeviceDeleteView(views.APIView):
    """
    Use this endpoint to delete a TOTP device by admin
    """
    permission_classes = (IsSuperAdmin,)

    def post(self, request, format=None):
        user_id = request.data.get('user_id')
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)

        devices = devices_for_user(user, confirmed=None)
        if not any(devices):
            return Response({'message': f'No TOTP device found for user {user.username}'},
                            status=status.HTTP_400_BAD_REQUEST)
        devices = devices_for_user(user, confirmed=None)
        for device in devices:
            device.delete()
            user.otp_device = False
            user.save()

        return Response({'message': f'TOTP devices deleted for user {user.username}'}, status=status.HTTP_200_OK)


class ReportsFiltersView(APIView):
    http_method_names = ['get']
    permission_classes = [IsAuth]

    @staticmethod
    def get(request):
        year_list = list(UserReportDetails.objects.values_list('report_year', flat=True).order_by(
            '-report_year').distinct())
        response = {'years': year_list}
        return Response(data=response, status=status.HTTP_200_OK)


class CustomTokenRefreshView(TokenRefreshView):
    serializer_class = CustomTokenRefreshSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except TokenError as e:
            raise InvalidToken(e.args[0])

        return ResponseV2(serializer.validated_data, status=status.HTTP_200_OK)


class NewDapCustomTokenRefreshView(CustomTokenRefreshView):
    serializer_class = NewDapCustomTokenRefreshSerializer


class SendAnalysisDates(APIView):
    gcp_project = 'webdata-207211'

    def authenticate_cloud_request(self):
        fernet = Fernet(settings.REPORT_API_KEY)
        try:
            value = self.request.META['HTTP_CLOUD_AUTHORIZATION']
        except KeyError:
            raise GenericAPIException('permission denied', status_code=403)
        try:
            decrypt_value = fernet.decrypt(value).decode()
        except CInvalidToken:
            raise GenericAPIException('invalid token', status_code=401)
        if decrypt_value != settings.REPORT_API_TOKEN:
            raise GenericAPIException('invalid token', status_code=401)

    def post(self, request):
        self.authenticate_cloud_request()
        self.send_reports()
        return Response(status=status.HTTP_200_OK)

    def send_reports(self):
        all_datasets = dict()
        context_data = dict()
        datasets = ['SINGLEFAMILY', 'MULTIFAMILY', 'SELF STORAGE', 'SHOPPING CENTER', 'OFFICE']
        client = bigquery.Client(project=self.gcp_project)
        for dataset in datasets:
            query = reports_mapping[dataset]['weekly']['analysis_date_query']
            ticker_dates = {data['ticker']: datetime.strptime(data['analysis_date'], '%Y-%m-%d').date() for data in
                            [loads(item[0]) for item in client.query(
                                query.format(ticker_query_condition=reports_ticker_query_condition[dataset])).result()]}

            all_datasets[dataset] = ticker_dates
        context_data['data'] = all_datasets

        user_email = settings.ANALYSIS_DATES_EMAIL
        send_email_to_user(to_email=[user_email], data=context_data, _for='analysis_dates')


class UserUnSubscribedReportsDetails(APIView):
    http_method_names = ['post', 'get']
    permission_classes = [IsAdmin]

    queryset = SubscribeReports.objects.all()

    def post(self, request):
        user_id = request.query_params.get('users')
        subscribed_users = list(
            self.queryset.filter(Q(weekly=True) | Q(monthly=True)).select_related('user').values_list(
                'user__id', flat=True).distinct())
        queryset = User.objects.exclude(id__in=subscribed_users).values('id', 'username', 'email')
        if request.query_params.get('show_clients'):
            queryset = User.objects.filter(Q(is_client=self.refactor_param_val(self.request.query_params.get(
                'show_clients').lower()))).exclude(id__in=subscribed_users).values('id', 'username', 'email')
        if request.query_params.get('is_active'):
            queryset = queryset.filter(Q(is_active=self.refactor_param_val(self.request.query_params.get(
                'is_active').lower()))).exclude(id__in=subscribed_users).values('id', 'username', 'email')

        if user_id:
            queryset = [item for item in queryset if item['id'] == int(user_id)]

        result = list(queryset)
        page_size = int(request.query_params.get("page_size", 25)) if int(
            request.query_params.get("page_size", 25)) in range(
            1, 101) else 25
        paginator = Paginator(result, page_size)
        page_number = int(request.query_params.get("page", 1))
        total_pages = paginator.num_pages
        if page_number <= total_pages:
            page_obj = paginator.get_page(page_number)
            response = {"page_count": total_pages, "data": page_obj.object_list}
        else:
            raise GenericAPIException(f"page not found")
        return Response(data=response, status=status.HTTP_200_OK)

    def get_users(self, listed_user=None):
        subscribed_users = list(
            self.queryset.filter(Q(weekly=True) | Q(monthly=True)).select_related('user').values_list(
                'user__id', flat=True).distinct())
        queryset = User.objects.exclude(id__in=subscribed_users).values('id', 'username', 'email')
        if listed_user:
            queryset = queryset.filter(Q(username__icontains=listed_user))
        return list(queryset)

    def get(self, request):
        if request.query_params.get('listed') and request.query_params.get('listed').lower() == 'true':
            if request.query_params.get('listed_user'):
                listed_user = request.query_params.get('listed_user')
                data = self.get_users(listed_user)
            else:
                data = self.get_users()
            return Response(data)

    @staticmethod
    def refactor_param_val(param):
        key_value = {'true': True, 'false': False}
        return key_value.get(param)


class UploadReports(APIView):
    http_method_names = ['post']
    send_reports_func = SendReports()
    history_upload = True
    approved = True
    file_type = 'pdf'

    def post(self, request):
        serializer = UploadReportsSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        file_path = validated_data.get('file_path')
        dataset = validated_data.get('dataset')[0]
        interval = validated_data.get('timeframe')
        sent_at = validated_data.get('sent_at')
        published_at = validated_data.get('published_at')
        try:
            self.send_reports_func.save_report_files_url(
                dataset, interval, file_path, published_at, self.file_type, self.approved, sent_at, self.history_upload)
        except:
            return Response({'details': 'file uploading failed.'}, 400)
        return Response(status=200)


class SaveFCMTokenView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def patch(self, request):
        fcm_id = request.data.get('fcm_id', None)
        if not fcm_id:
            return Response({"error": "FCM ID is required"}, status=status.HTTP_400_BAD_REQUEST)
        request.user.fcm_id = fcm_id
        request.user.save()
        return Response({"message": "FCM ID saved successfully"}, status=status.HTTP_200_OK)


class ReadNotification(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        serializer = ReadNotificationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        notification_id = validated_data.get('notification_id')

        if not firebase_admin._apps:
            cred = credentials.Certificate(settings.FIREBASE_KEY_FILE)
            firebase_admin.initialize_app(cred)
        firestore_db = firestore.client()
        collection_name = f"users_{settings.ENV}"
        user_key = f"user_{str(request.user.id)}_notifications"
        user_ref = firestore_db.collection(collection_name).document(user_key)
        user_doc = user_ref.get()
        timestamp = int(time.time())
        if user_doc.exists:
            notifications = user_doc.to_dict().get('notifications', [])
            updated_notifications = []
            notification_found = False

            for notification in notifications:
                if notification['notification_id'] == int(notification_id):
                    notification['is_read'] = True
                    notification['timestamp'] = timestamp
                    notification_found = True
                updated_notifications.append(notification)

            if notification_found:
                try:
                    user_ref.update({'notifications': updated_notifications})
                    logger.info(
                        f"Notification updated successfully for user id {request.user.id} and notification id {notification_id}")
                except Exception as e:
                    logger.error(
                        f"Error updating notification: {e} for user id {request.user.id} and notification id {notification_id}")
            else:
                logger.warning(f"No notification found with id {notification_id} for user id {request.user.id}")
        else:
            logger.warning(f"User document does not exist for user id {request.user.id}")

        return Response(status=status.HTTP_200_OK)