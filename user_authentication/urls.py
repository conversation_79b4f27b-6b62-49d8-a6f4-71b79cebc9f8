from django.urls import path
from django.conf import settings
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SimpleRouter

from .models import ServiceCategory
from .views import (SignupView, CustomTokenObtainPairView, CustomTokenRefreshView, OTPView, OTPVerifyView,
                    ForgotPasswordView, ResetPasswordView, LogoutView, PropertyOwnerProfileView, KYCRequests,
                    KYCView, VendorProfileView, KYCRequestDetails, ServiceCategoriesView, ServiceSubCategoriesView,
                    SelectRoleView)
if settings.DEBUG:
    router = DefaultRouter()
else:
    router = SimpleRouter()
router.register('service-categories', ServiceCategoriesView)
router.register('service-subcategories', ServiceSubCategoriesView)
urlpatterns = [
    path('signup/', SignupView.as_view(), name='signup'),
    path('user-details/', SignupView.as_view(), name='user-detail'),
    path('add-role/', SelectRoleView.as_view(), name='add_role'),
    path('token/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', CustomTokenRefreshView.as_view(), name='token_refresh'),
    path('otp/', OTPView.as_view(), name='otp'),
    path('otp-verify/', OTPVerifyView.as_view(), name='otp'),
    path('forgot-password/', ForgotPasswordView.as_view(), name='forgot_password'),
    path('reset-password/', ResetPasswordView.as_view(), name='reset_password'),
    path('logout/', LogoutView.as_view(), name='logout'),
    path('property-owner-profile/', PropertyOwnerProfileView.as_view(), name='property_owner_profile'),
    path('vendor-profile/', VendorProfileView.as_view(), name='vendor_profile'),
    path('kyc-request/', KYCRequests.as_view(), name='kyc_request'),
    path('kyc-request-detail/<int:pk>/', KYCRequestDetails.as_view(), name='kyc_request_detail'),
    path('kyc/', KYCView.as_view(), name='kyc_list'),
    path('kyc/stats/', KYCView.as_view(), name='kyc_stats'),
]
urlpatterns += router.urls