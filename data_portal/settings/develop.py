from data_portal.settings.base import *
import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration
from google.oauth2 import service_account

ALLOWED_HOSTS = ['localhost', '127.0.0.1', 'dataportal-bknd-dvlp-dot-webdata-207211.ew.r.appspot.com',
                 'dataportal-develop2-dot-webdata-207211.ew.r.appspot.com', 'bdadevs.com',
                 'dataportal-pre-develop-dot-webdata-207211.ew.r.appspot.com',
                 'dapapi.bdadevs.com']

CORS_ORIGIN_ALLOW_ALL = False
CORS_ORIGIN_WHITELIST = (
    'https://127.0.0.1:3000',
    'https://localhost:3000',
    'http://localhost:3000',
    'https://dataportal-develop-dot-webdata-207211.ew.r.appspot.com',
    'https://dataportal-bknd-dvlp-dot-webdata-207211.ew.r.appspot.com',
    'https://dataportal-develop2-dot-webdata-207211.ew.r.appspot.com',
    'http://bdadevs.com',
    'https://bdadevs.com',
    'https://www.bdadevs.com',
    'https://dataportal-pre-develop-dot-webdata-207211.ew.r.appspot.com',
    'http://dapapi.bdadevs.com',
    'https://dapapi.bdadevs.com'
)
CORS_ALLOW_CREDENTIALS = True

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.BasicAuthentication',
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ],

}

DOMAIN = config.get('DOMAIN')
SITE_NAME = config.get('DOMAIN')

ENV = config.get('env', 'develop')

sentry_sdk.init(
    dsn=config.get('DSN'),
    integrations=[DjangoIntegration()],
    traces_sample_rate=1.0 if not DEBUG else 0.5,
    send_default_pii=True,
    environment=ENV
)
# RAVEN_CONFIG = {
#     'dsn': config.get('DSN_LOGGING'),
# }
DEBUG = False

DEFAULT_FILE_STORAGE = 'data_portal.gcloud.GoogleCloudMediaFileStorage'
GS_CREDENTIALS = service_account.Credentials.from_service_account_file(
    config.get('GOOGLE_CRED_FILE_PATH')
)
GS_BUCKET_NAME = 'email-template-intellistocks'
GS_REPORT_BUCKET_NAME = 'dap-reports'
BUCKET_FOLDER = 'data-portal/users-dashboards'
BUCKET_FOLDER_REPORTS = 'dataset_reports/develop/'
BUCKET_FOLDER_PROFILE = 'data-portal/users-profile-pics'
BUCKET_FOLDER_NOTIFICATIONS = 'dataset_notifications/develop/'
GS_NOTIFICATIONS_BUCKET = config.get('GS_NOTIFICATIONS_BUCKET')
DASHBOARD_CDN = 'https://cdn.bigbyteinsights.com'
BUCKET_FOLDER_CSV = 'data-portal/csv/develop/'

BUCKET_CREDENTIALS = service_account.Credentials.from_service_account_file(filename=config.get('BUCKET_CRED_FILE_PATH'))

os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = config.get('GOOGLE_CRED_FILE_PATH')

PROJECT_NAME = config.get('PROJECT_NAME')
DOWNLOAD_REPORTS_PROJ = config.get('DOWNLOAD_REPORTS_PROJECT_NAME')
TEST_ADMIN = config.get('TEST_ADMIN_CREDENTIALS')

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
        },
        'sentry': {
            'level': 'INFO',  # Ensure INFO level logs are sent to Sentry
            'class': 'sentry_sdk.integrations.logging.EventHandler',
        },
    },
    'root': {
        'handlers': ['console', 'sentry'],
        'level': 'INFO',  # Change this to capture INFO and higher levels
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'sentry'],
            'level': 'INFO',  # Logs INFO level messages
            'propagate': True,
        },
    },
}
