<!DOCTYPE html>
<html>
<head>
    {% load custom_tags %}
    <title>Analysis Table</title>
    <style>
        table {
            table-layout: auto;
            border-collapse: collapse;
            width: 50%;
        }
        .table-container {
            display: grid;
            grid-gap: 20px;
            grid-auto-rows: auto;
            justify-content: center;
        }
        th, td {
            border: 1px solid black;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
<div class="table-container">
    <table>
        <tr>
            <th style="font-size:14px;"><strong>Tickers</strong></th>
            <th style="font-size:14px;"><strong>Latest Analysis Dates</strong></th>
        </tr>
      {% for dataset, tickers in data.items %}
            <tr>
                <th colspan="2" style="padding-left:250px;font-size:13px;">{{dataset}}</th>
            </tr>
            {% for ticker, analysis_date in tickers.items %}
                {% if analysis_date|outdated_ticker %}
                    <tr>
                        <td>{{ ticker.upper }}</td>
                        <td>{{ analysis_date }}</td>
                    </tr>
                {% else %}
                    <tr>
                        <td style="color: red;"><strong>{{ ticker.upper }}</strong></td>
                        <td>{{ analysis_date }}</td>
                    </tr>
                {% endif %}
            {% endfor %}
      {% endfor %}
    </table>
</div>
</body>
</html>
