# Generated by Django 3.2.2 on 2023-01-30 11:59

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('self_storage', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='sspermissions',
            options={'managed': False, 'permissions': (('Can view self_storage dashboard', 'view_self_storage_dashboard'), ('Can access self_storage api', 'access_self_storage_api'), ('Can download self_storage csv', 'download_self_storage_csv'), ('Can access self_storage bucket', 'bucket_self_storage_data'))},
        ),
    ]
