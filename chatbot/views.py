import json
import logging
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.conf import settings
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status

from .services.enhanced_chatbot import EnhancedChatbot

logger = logging.getLogger(__name__)

# Initialize chatbot instance
chatbot = None

def get_chatbot_instance():
    """Get or create chatbot instance"""
    global chatbot
    if chatbot is None:
        try:
            chatbot = EnhancedChatbot(
                gemini_api_key=getattr(settings, 'GEMINI_API_KEY', None),
                db_config=getattr(settings, 'RENTAL_GURU_DB', None)
            )
            logger.info("Chatbot instance created successfully")
        except Exception as e:
            logger.error(f"Failed to create chatbot instance: {str(e)}")
            raise
    return chatbot


@api_view(['POST'])
def chat(request):
    """
    Main chatbot endpoint
    Accepts POST requests with user queries and returns AI responses
    """
    try:
        # Get user query from request
        data = request.data
        user_query = data.get('query', '').strip()

        if not user_query:
            return Response({
                'error': 'Query is required',
                'response': 'Please provide a question or query.'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get chatbot instance and process query
        bot = get_chatbot_instance()
        result = bot.process_query(user_query)

        # Return response
        return Response({
            'success': True,
            'query': user_query,
            'response': result['response'],
            'response_type': result.get('response_type', 'unknown'),
            'intent_classification': result.get('intent_classification'),
            'data_results': result.get('data_results'),
            'sql_query': result.get('sql_query'),
            'row_count': result.get('row_count'),
            'error': result.get('error')
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error in chat endpoint: {str(e)}")
        return Response({
            'success': False,
            'error': 'Internal server error',
            'response': 'I apologize, but I encountered an error. Please try again.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def system_status(request):
    """
    Get system status and health check
    """
    try:
        bot = get_chatbot_instance()
        status_info = bot.get_system_status()

        return Response({
            'success': True,
            'status': status_info,
            'available_tables': bot.get_available_tables()
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error in system status: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def refresh_schema(request):
    """
    Refresh database schema cache
    """
    try:
        bot = get_chatbot_instance()
        bot.refresh_schema_cache()

        return Response({
            'success': True,
            'message': 'Schema cache refreshed successfully'
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error refreshing schema: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def get_table_info(request, table_name):
    """
    Get detailed information about a specific table
    """
    try:
        bot = get_chatbot_instance()
        table_info = bot.get_table_info(table_name)

        if table_info:
            return Response({
                'success': True,
                'table_name': table_name,
                'table_info': table_info
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'error': f'Table {table_name} not found'
            }, status=status.HTTP_404_NOT_FOUND)

    except Exception as e:
        logger.error(f"Error getting table info: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Legacy function for backward compatibility
def get_chatbot_response(query: str):
    """Legacy function - use the new chat endpoint instead"""
    try:
        bot = get_chatbot_instance()
        result = bot.process_query(query)
        print(f"Query: {query}")
        print(f"Response: {result['response']}")
        return result['response']
    except Exception as e:
        print(f"Error: {str(e)}")
        return "Error processing query"