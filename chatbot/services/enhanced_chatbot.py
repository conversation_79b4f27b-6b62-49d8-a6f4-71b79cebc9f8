"""
Enhanced Chatbot Service
Main orchestrator that combines intent classification, database queries, and response generation
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from django.conf import settings

from .gemini_service import GeminiService
from .database_inspector import DatabaseInspector

logger = logging.getLogger(__name__)


class EnhancedChatbot:
    """
    Main chatbot service that handles:
    1. Intent classification (FAQ vs Data Query)
    2. Database schema inspection and querying
    3. Natural language response generation
    """
    
    def __init__(self, gemini_api_key: Optional[str] = None, db_config: Optional[Dict] = None):
        """Initialize enhanced chatbot with required services"""
        
        # Initialize services
        self.gemini_service = GeminiService(api_key=gemini_api_key)
        self.db_inspector = DatabaseInspector(db_config=db_config)
        
        # Load intents data
        self.intents_data = self._load_intents_data()
        
        # Cache for database schema (to avoid repeated queries)
        self._schema_cache = None
        
        logger.info("Enhanced chatbot initialized successfully")

    def process_query(self, user_query: str) -> Dict:
        """
        Main method to process user queries
        Returns comprehensive response with metadata
        """
        try:
            logger.info(f"Processing query: {user_query}")
            
            # Step 1: Classify intent
            intent_result = self.gemini_service.classify_intent(user_query, self.intents_data)
            
            response_data = {
                'user_query': user_query,
                'intent_classification': intent_result,
                'response': '',
                'data_results': None,
                'sql_query': None,
                'processing_time': None,
                'error': None
            }
            
            # Step 2: Handle based on intent type
            if intent_result.get('intent_type') == 'faq':
                # Handle FAQ queries
                response_data['response'] = self._handle_faq_query(intent_result)
                response_data['response_type'] = 'faq'
                
            elif intent_result.get('intent_type') == 'data_query':
                # Handle data queries
                data_response = self._handle_data_query(user_query)
                response_data.update(data_response)
                response_data['response_type'] = 'data_query'
                
            else:
                # Fallback for unclear intents
                response_data['response'] = self._handle_fallback_query(user_query)
                response_data['response_type'] = 'fallback'
            
            logger.info(f"Query processed successfully: {intent_result.get('intent_type')}")
            return response_data
            
        except Exception as e:
            logger.error(f"Error processing query: {str(e)}")
            return {
                'user_query': user_query,
                'intent_classification': None,
                'response': "I apologize, but I encountered an error while processing your request. Please try again.",
                'data_results': None,
                'sql_query': None,
                'response_type': 'error',
                'error': str(e)
            }

    def _handle_faq_query(self, intent_result: Dict) -> str:
        """Handle FAQ-type queries using predefined responses"""
        try:
            intent_data = intent_result.get('intent_data')
            if intent_data:
                response = intent_data.get('response', '')
                
                # Add quick actions if available
                quick_actions = intent_data.get('quick_actions', [])
                if quick_actions:
                    response += "\n\nQuick actions you can take:"
                    for action in quick_actions[:2]:  # Limit to 2 actions
                        response += f"\n• {action.get('text', '')}"
                
                return response
            else:
                return "I found a matching topic, but I don't have detailed information available right now."
                
        except Exception as e:
            logger.error(f"Error handling FAQ query: {str(e)}")
            return "I can help with that topic, but I need more specific information."

    def _handle_data_query(self, user_query: str) -> Dict:
        """Handle data queries that require database access"""
        try:
            # Get database schema
            schema = self._get_database_schema()
            if not schema:
                return {
                    'response': "I'm sorry, but I can't access the database right now. Please try again later.",
                    'data_results': None,
                    'sql_query': None,
                    'error': 'Database schema not available'
                }
            
            # Generate SQL query
            sql_result = self.gemini_service.generate_sql_query(user_query, schema)
            sql_query = sql_result.get('sql_query')
            
            if not sql_query:
                return {
                    'response': "I understand your question, but I'm having trouble generating the right database query. Could you rephrase your question?",
                    'data_results': None,
                    'sql_query': None,
                    'error': sql_result.get('error', 'SQL generation failed')
                }
            
            # Execute query
            query_result = self.db_inspector.execute_query(sql_query)
            
            if query_result.get('error'):
                return {
                    'response': "I generated a query for your question, but there was an error executing it. The database might be temporarily unavailable.",
                    'data_results': None,
                    'sql_query': sql_query,
                    'error': query_result['error']
                }
            
            # Generate natural language response
            response = self.gemini_service.generate_response(
                user_query, 
                context_data={'intent_type': 'data_query'},
                query_result=query_result['results']
            )
            
            return {
                'response': response,
                'data_results': query_result['results'],
                'sql_query': sql_query,
                'sql_explanation': sql_result.get('explanation', ''),
                'row_count': query_result.get('row_count', 0),
                'error': None
            }
            
        except Exception as e:
            logger.error(f"Error handling data query: {str(e)}")
            return {
                'response': "I encountered an error while trying to fetch the data you requested. Please try rephrasing your question.",
                'data_results': None,
                'sql_query': None,
                'error': str(e)
            }

    def _handle_fallback_query(self, user_query: str) -> str:
        """Handle queries that don't clearly fit FAQ or data categories"""
        try:
            # Use Gemini to generate a helpful response
            prompt = f"""
You are a helpful assistant for a property management system. The user asked: "{user_query}"

This query doesn't clearly fit into our FAQ categories or data queries. Provide a helpful response that:
1. Acknowledges their question
2. Suggests how they might rephrase it for better results
3. Offers general guidance about what you can help with

Keep it friendly and concise.
"""
            
            response = self.gemini_service.model.generate_content(
                prompt,
                generation_config=self.gemini_service.generation_config
            )
            
            return response.text.strip()
            
        except Exception as e:
            logger.error(f"Error in fallback handler: {str(e)}")
            return "I'm not sure how to help with that specific question. Could you try asking about property management tasks or requesting specific data about your properties?"

    def _get_database_schema(self) -> Dict:
        """Get database schema with caching"""
        if self._schema_cache is None:
            self._schema_cache = self.db_inspector.get_database_schema()
        return self._schema_cache

    def _load_intents_data(self) -> Dict:
        """Load intents data from JSON file"""
        try:
            # Try to find intents.json in the chatbot data directory
            current_dir = Path(__file__).resolve().parent.parent
            intents_path = current_dir / 'data' / 'intents.json'
            
            if not intents_path.exists():
                logger.warning(f"Intents file not found at {intents_path}")
                return {'intents': [], 'settings': {}}
            
            with open(intents_path, 'r', encoding='utf-8') as f:
                intents_data = json.load(f)
            
            logger.info(f"Loaded {len(intents_data.get('intents', []))} intents")
            return intents_data
            
        except Exception as e:
            logger.error(f"Error loading intents data: {str(e)}")
            return {'intents': [], 'settings': {}}

    def get_system_status(self) -> Dict:
        """Get status of all system components"""
        status = {
            'gemini_service': False,
            'database_connection': False,
            'intents_loaded': False,
            'schema_cached': self._schema_cache is not None
        }
        
        try:
            # Test Gemini connection
            status['gemini_service'] = self.gemini_service.test_connection()
            
            # Test database connection
            status['database_connection'] = self.db_inspector.test_connection()
            
            # Check intents
            status['intents_loaded'] = len(self.intents_data.get('intents', [])) > 0
            
        except Exception as e:
            logger.error(f"Error checking system status: {str(e)}")
        
        return status

    def refresh_schema_cache(self):
        """Refresh the cached database schema"""
        self._schema_cache = None
        self._get_database_schema()
        logger.info("Database schema cache refreshed")

    def get_available_tables(self) -> List[str]:
        """Get list of available database tables"""
        schema = self._get_database_schema()
        return list(schema.keys()) if schema else []

    def get_table_info(self, table_name: str) -> Optional[Dict]:
        """Get detailed information about a specific table"""
        schema = self._get_database_schema()
        return schema.get(table_name) if schema else None
