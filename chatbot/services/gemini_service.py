"""
Gemini AI Service for chatbot functionality
Handles intent classification, SQL generation, and response generation
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any
import google.generativeai as genai
from django.conf import settings

logger = logging.getLogger(__name__)


class GeminiService:
    """Service class for Google Gemini AI integration"""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize Gemini service with API key"""
        self.api_key = api_key or getattr(settings, 'GEMINI_API_KEY', None)
        if not self.api_key:
            raise ValueError("Gemini API key is required. Set GEMINI_API_KEY in settings or pass as parameter.")
        
        # Configure Gemini
        genai.configure(api_key=self.api_key)
        
        # Initialize model
        self.model = genai.GenerativeModel('gemini-1.5-flash')
        
        # Generation config for consistent responses
        self.generation_config = genai.types.GenerationConfig(
            temperature=0.1,
            top_p=0.8,
            top_k=40,
            max_output_tokens=2048,
        )
        
        logger.info("Gemini service initialized successfully")

    def classify_intent(self, user_query: str, intents_data: Dict) -> Dict:
        """
        Classify user intent using Gemini
        Returns whether query is FAQ-related or requires database access
        """
        try:
            # Build intent descriptions for prompt
            intent_descriptions = []
            for intent in intents_data.get('intents', []):
                description = intent.get('description', intent.get('response', '')[:150])
                intent_descriptions.append(
                    f"- {intent['page_id']}: {description}"
                )
            
            intents_text = '\n'.join(intent_descriptions)
            
            prompt = f"""
You are an intelligent assistant for a property management system. Analyze the user query and determine:

1. Is this a FAQ/navigation question that can be answered from the predefined intents?
2. Or is this a data query that requires database access?

AVAILABLE FAQ INTENTS:
{intents_text}

USER QUERY: "{user_query}"

Respond with ONLY a JSON object in this exact format:
{{
    "intent_type": "faq" or "data_query",
    "confidence": 0.0-1.0,
    "matched_intent": "page_id" (only if intent_type is "faq", otherwise null),
    "reasoning": "brief explanation"
}}

RULES:
- Use "faq" if the query matches any of the predefined intents above
- Use "data_query" if the query asks for specific data, reports, statistics, or information not covered in FAQs
- Be precise with confidence scores
- Only return valid JSON
"""

            response = self.model.generate_content(
                prompt,
                generation_config=self.generation_config
            )
            
            # Parse response
            result = json.loads(response.text.strip())
            
            # Add matched intent details if FAQ
            if result.get('intent_type') == 'faq' and result.get('matched_intent'):
                matched_intent = self._find_intent_by_id(result['matched_intent'], intents_data)
                if matched_intent:
                    result['intent_data'] = matched_intent
            
            return result
            
        except Exception as e:
            logger.error(f"Error in intent classification: {str(e)}")
            return {
                'intent_type': 'data_query',
                'confidence': 0.5,
                'matched_intent': None,
                'reasoning': 'Error in classification, defaulting to data query',
                'error': str(e)
            }

    def generate_sql_query(self, user_query: str, database_schema: Dict) -> Dict:
        """
        Generate SQL query from natural language using database schema
        """
        try:
            # Format schema information
            schema_text = self._format_schema_for_prompt(database_schema)
            
            prompt = f"""
You are an expert SQL query generator for a PostgreSQL rental property management database.

DATABASE SCHEMA:
{schema_text}

USER QUERY: "{user_query}"

Generate a PostgreSQL SQL query to answer the user's question. Follow these rules:

1. Use only tables and columns that exist in the schema
2. Use proper PostgreSQL syntax
3. Include appropriate JOINs when needed
4. Use LIMIT for potentially large result sets
5. Handle case-insensitive searches with ILIKE when appropriate
6. Use proper data types and formatting

Respond with ONLY a JSON object:
{{
    "sql_query": "SELECT ... FROM ... WHERE ...",
    "explanation": "Brief explanation of what the query does",
    "estimated_complexity": "low|medium|high",
    "tables_used": ["table1", "table2"]
}}

Only return valid JSON with a working PostgreSQL query.
"""

            response = self.model.generate_content(
                prompt,
                generation_config=self.generation_config
            )
            
            result = json.loads(response.text.strip())
            return result
            
        except Exception as e:
            logger.error(f"Error in SQL generation: {str(e)}")
            return {
                'sql_query': None,
                'explanation': 'Error generating SQL query',
                'estimated_complexity': 'high',
                'tables_used': [],
                'error': str(e)
            }

    def generate_response(self, user_query: str, context_data: Optional[Dict] = None, 
                         query_result: Optional[List] = None) -> str:
        """
        Generate natural language response using context and query results
        """
        try:
            if context_data and context_data.get('intent_type') == 'faq':
                # FAQ response
                intent_data = context_data.get('intent_data', {})
                return intent_data.get('response', 'I can help you with that, but I need more specific information.')
            
            elif query_result is not None:
                # Data query response
                prompt = f"""
You are a helpful assistant for a property management system. 

USER QUESTION: "{user_query}"

DATABASE QUERY RESULTS:
{json.dumps(query_result, indent=2, default=str)}

Generate a natural, conversational response that:
1. Directly answers the user's question
2. Presents the data in an easy-to-understand format
3. Includes relevant insights if applicable
4. Is concise but informative
5. Uses friendly, professional tone

If the results are empty, explain that no matching data was found and suggest alternatives.
If there are many results, summarize the key findings.

Respond with just the natural language answer, no JSON or formatting.
"""

                response = self.model.generate_content(
                    prompt,
                    generation_config=self.generation_config
                )
                
                return response.text.strip()
            
            else:
                # Fallback response
                return "I understand your question, but I need more information to provide a helpful answer. Could you please be more specific?"
                
        except Exception as e:
            logger.error(f"Error in response generation: {str(e)}")
            return "I apologize, but I encountered an error while processing your request. Please try again."

    def _find_intent_by_id(self, page_id: str, intents_data: Dict) -> Optional[Dict]:
        """Find intent data by page_id"""
        for intent in intents_data.get('intents', []):
            if intent.get('page_id') == page_id:
                return intent
        return None

    def _format_schema_for_prompt(self, schema: Dict) -> str:
        """Format database schema for inclusion in prompts"""
        schema_lines = []
        
        for table_name, table_info in schema.items():
            schema_lines.append(f"\nTable: {table_name}")
            schema_lines.append(f"Description: {table_info.get('description', 'Property management table')}")
            schema_lines.append("Columns:")
            
            for column in table_info.get('columns', []):
                col_line = f"  - {column['name']} ({column['type']})"
                if column.get('nullable'):
                    col_line += " [nullable]"
                if column.get('description'):
                    col_line += f" - {column['description']}"
                schema_lines.append(col_line)
        
        return '\n'.join(schema_lines)

    def test_connection(self) -> bool:
        """Test Gemini API connection"""
        try:
            response = self.model.generate_content(
                "Respond with 'OK' if you can read this message.",
                generation_config=genai.types.GenerationConfig(
                    temperature=0,
                    max_output_tokens=10
                )
            )
            return response.text.strip().upper() == 'OK'
        except Exception as e:
            logger.error(f"Gemini connection test failed: {str(e)}")
            return False
