from datetime import date
from single_family.views import (SingleFamilyBigQueryViewSet, SingleFamilyCsvBigQueryViewSet,
                                 SingleFamilyRelativeAvgBigQueryViewSet, SingleFamilyRelativeAverageBigQueryViewSet,
                                 SingleFamilyMsaSummaryBigQueryViewSet, SingleFamilyAllMetricsCsvBigQueryViewSet,
                                 SingleFamilyCsvMsaSummaryBigQueryViewSet, SingleFamilyAssetCountBigQueryViewSet)


class MultiFamilyBigQueryViewSet(SingleFamilyBigQueryViewSet):
    db = 'multi_family'
    app_label = 'multi_family'
    dataset = 'MULTIFAMILY'
    dashboard_perm = 'multi_family.Can view multi_family dashboard'

    def get_query_filters(self, filters):
        if self.metric == 'yy_differential':
            filters_list = [f"c.ticker = '{self.ticker[0].upper()}'" if len(
                self.ticker) == 1 else f"c.ticker in {tuple([ticker.upper() for ticker in self.ticker])}"]
            if 'portfolio_year' in filters:
                filters_list.append(f"c.portfolio_year = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"c.portfolio_year IN {tuple(filters['portfolio_year'])}")
            if 'msa' in filters:
                filters_list.append(f"c.msa = '{filters['msa'][0]}'" if
                                    len(filters['msa']) == 1 else
                                    f"c.msa IN {tuple(filters['msa'])}")
        else:
            filters_list = [f"ticker = '{self.ticker[0].upper()}'" if len(
                self.ticker) == 1 else f"ticker in {tuple([ticker.upper() for ticker in self.ticker])}"]
        if self.metric in ['avg_rent', 'median_rent', 'rent_per_square_foot']:
            if 'portfolio_year' in filters:
                filters_list.append(f"EXTRACT(YEAR FROM created) = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"EXTRACT(YEAR FROM created) IN {tuple(filters['portfolio_year'])}")
            if 'msa' in filters:
                filters_list.append(f"msa = '{filters['msa'][0]}'" if
                                    len(filters['msa']) == 1 else
                                    f"msa IN {tuple(filters['msa'])}")
            if 'beds' in filters:
                filters_list.append(f"beds = '{filters['beds'][0]}'" if
                                    len(filters['beds']) == 1 else
                                    f"beds IN {tuple([str(bed) for bed in filters['beds']])}")
        elif self.metric in ['lol', 'new_rentals_yoy', 'new_rentals_yoy2', 'median_lease_duration']:
            if 'portfolio_year' in filters:
                filters_list.append(f"portfolio_year = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"portfolio_year IN {tuple(filters['portfolio_year'])}")
            if 'msa' in filters:
                filters_list.append(f"msa = '{filters['msa'][0]}'" if
                                    len(filters['msa']) == 1 else
                                    f"msa IN {tuple(filters['msa'])}")
        elif self.metric == 'days_market':
            if 'portfolio_year' in filters:
                filters_list.append(f"EXTRACT(YEAR FROM created) = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"EXTRACT(YEAR FROM created) IN {tuple(filters['portfolio_year'])}")
            if 'msa' in filters:
                filters_list.append(f"msa = '{filters['msa'][0]}'" if
                                    len(filters['msa']) == 1 else
                                    f"msa IN {tuple(filters['msa'])}")
        elif self.metric in ['median_lead_days']:
            filters_list.append(f"days_to_rent NOT IN (0,1)")
            if 'portfolio_year' in filters:
                filters_list.append(f"portfolio_year = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"portfolio_year IN {tuple(filters['portfolio_year'])}")
        filters = ' AND '.join(filters_list)
        return {'filters': filters}


class MultiFamilyCsvBigQueryViewSet(SingleFamilyCsvBigQueryViewSet):
    db = 'multi_family'
    app_label = 'multi_family'
    dataset = 'MULTIFAMILY'
    dashboard_perm = 'multi_family.Can download multi_family csv'

    def get_query_filters(self, filters):
        if self.metric == 'yy_differential':
            filters_list = [f"c.ticker = '{self.ticker[0].upper()}'" if len(
                self.ticker) == 1 else f"c.ticker in {tuple([ticker.upper() for ticker in self.ticker])}"]
            if 'portfolio_year' in filters:
                filters_list.append(f"c.portfolio_year = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"c.portfolio_year IN {tuple(filters['portfolio_year'])}")
            if 'msa' in filters:
                filters_list.append(f"c.msa = '{filters['msa'][0]}'" if
                                    len(filters['msa']) == 1 else
                                    f"c.msa IN {tuple(filters['msa'])}")
        else:
            filters_list = [f"ticker = '{self.ticker[0].upper()}'" if len(
                self.ticker) == 1 else f"ticker in {tuple([ticker.upper() for ticker in self.ticker])}"]
        if self.metric in ['avg_rent', 'median_rent', 'rent_per_square_foot']:
            if 'portfolio_year' in filters:
                filters_list.append(f"EXTRACT(YEAR FROM created) = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"EXTRACT(YEAR FROM created) IN {tuple(filters['portfolio_year'])}")
            if 'msa' in filters:
                filters_list.append(f"msa = '{filters['msa'][0]}'" if
                                    len(filters['msa']) == 1 else
                                    f"msa IN {tuple(filters['msa'])}")
            if 'beds' in filters:
                filters_list.append(f"beds = '{filters['beds'][0]}'" if
                                    len(filters['beds']) == 1 else
                                    f"beds IN {tuple([str(bed) for bed in filters['beds']])}")
        elif self.metric in ['lol', 'new_rentals_yoy', 'new_rentals_yoy2', 'median_lease_duration']:
            if 'portfolio_year' in filters:
                filters_list.append(f"portfolio_year = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"portfolio_year IN {tuple(filters['portfolio_year'])}")
            if 'msa' in filters:
                filters_list.append(f"msa = '{filters['msa'][0]}'" if
                                    len(filters['msa']) == 1 else
                                    f"msa IN {tuple(filters['msa'])}")
        elif self.metric == 'days_market':
            if 'portfolio_year' in filters:
                filters_list.append(f"EXTRACT(YEAR FROM created) = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"EXTRACT(YEAR FROM created) IN {tuple(filters['portfolio_year'])}")
            if 'msa' in filters:
                filters_list.append(f"msa = '{filters['msa'][0]}'" if
                                    len(filters['msa']) == 1 else
                                    f"msa IN {tuple(filters['msa'])}")
        elif self.metric in ['median_lead_days']:
            filters_list.append(f"days_to_rent NOT IN (0,1)")
            if 'portfolio_year' in filters:
                filters_list.append(f"portfolio_year = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"portfolio_year IN {tuple(filters['portfolio_year'])}")
        filters = ' AND '.join(filters_list)
        return {'filters': filters}


class MultiFamilyRelativeAvgBigQueryViewSet(SingleFamilyRelativeAvgBigQueryViewSet):
    db = 'multi_family'
    app_label = 'multi_family'
    dataset = 'MULTIFAMILY'
    dashboard_perm = 'multi_family.Can view multi_family dashboard'


class MultiFamilyRelativeAverageBigQueryViewSet(SingleFamilyRelativeAverageBigQueryViewSet):
    db = 'multi_family'
    app_label = 'multi_family'
    dataset = 'MULTIFAMILY'
    dashboard_perm = 'multi_family.Can view multi_family dashboard'


class MultiFamilyMsaSummaryBigQueryViewSet(SingleFamilyMsaSummaryBigQueryViewSet):
    db = 'multi_family'
    app_label = 'multi_family'
    dataset = 'MULTIFAMILY'
    dashboard_perm = 'multi_family.Can view multi_family dashboard'

    def get_query_filters(self, filters):
        date_range, date_range_yy = self.get_date_range()
        filters_list_yy_differential_1 = list()
        filters_list = [f"ticker = '{self.ticker[0].upper()}'" if len(
            self.ticker) == 1 else f"ticker in {tuple([ticker.upper() for ticker in self.ticker])}"]
        if self.metric == 'yy_differential':
            yy_ticker = (f"c.ticker = '{self.ticker[0].upper()}'" if len(
                self.ticker) == 1 else f"c.ticker in {tuple([ticker.upper() for ticker in self.ticker])}")
            filters_list.append(yy_ticker)
            filters_list_yy_differential_1.append(yy_ticker)
        if self.unit_area:
            filters_list.append(f"unit_area = '{self.unit_area}'")
        if "portfolio_year" in filters:
            if self.metric == 'yy_differential':
                yy_portfolio = (f"c.portfolio_year = {filters['portfolio_year'][0]}" if
                                len(filters['portfolio_year']) == 1 else
                                f"c.portfolio_year IN {tuple(filters['portfolio_year'])}")
                filters_list.append(yy_portfolio)
                filters_list_yy_differential_1.append(yy_portfolio)
            if self.metric in ['avg_rent', 'median_rent', 'rent_per_square_foot']:
                filters_list.append(f"EXTRACT(YEAR FROM created) = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"EXTRACT(YEAR FROM created) IN {tuple(filters['portfolio_year'])}")
            elif self.metric in ['lol', 'new_rentals_yoy', 'new_rentals_yoy2']:
                filters_list.append(f"portfolio_year = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"portfolio_year IN {tuple(filters['portfolio_year'])}")
            elif self.metric == 'days_market':
                filters_list.append(f"EXTRACT(YEAR FROM created) = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"EXTRACT(YEAR FROM created) IN {tuple(filters['portfolio_year'])}")

        filters_list.extend(date_range)
        filters_list_yy_differential_1.extend(date_range_yy)
        filters = ' AND '.join(filters_list)
        filters_list_yy_differential_1 = ' AND '.join(filters_list_yy_differential_1)
        return {'filters': filters, "filters_list_yy_differential_1": filters_list_yy_differential_1}

    def get_date_range(self):
        filters_list = list()
        filters_list_yy_differential_1 = list()  # yy_diff is difference of same month and 2 years
        if self.start_date:
            self.end_date = self.end_date if self.end_date else date.today()
            if self.metric in ['days_market']:
                filters_list.append(f"my_date BETWEEN "
                                    f"PARSE_DATE('%Y-%m-%d','{self.start_date}') AND "
                                    f"PARSE_DATE('%Y-%m-%d', '{self.end_date}')")
            else:
                filters_list.append(f"date BETWEEN "
                                    f"PARSE_DATE('%Y-%m-%d','{self.start_date}') AND "
                                    f"PARSE_DATE('%Y-%m-%d', '{self.end_date}')")
                if self.metric in ['yy_differential']:
                    filters_list_yy_differential_1.append(f"date BETWEEN "
                                                          f"DATE_SUB(PARSE_DATE('%Y-%m-%d','{self.start_date}'), INTERVAL 1 "
                                                          f"Year) AND "
                                                          f"DATE_SUB(PARSE_DATE('%Y-%m-%d', '{self.end_date}'), INTERVAL 1 Year)")
        return filters_list, filters_list_yy_differential_1


class MultiFamilyAllMetricsCsvBigQueryViewSet(SingleFamilyAllMetricsCsvBigQueryViewSet):
    db = 'multi_family'
    app_label = 'multi_family'
    dataset = 'MULTIFAMILY'
    dashboard_perm = 'multi_family.Can view multi_family dashboard'

    def get_query_filters(self, filters):
        filters_list_lol = list()
        filters_list_yy_differential_1 = list()
        filters_list_yy_differential_2 = list()
        filters_list_days_market = list()
        filters_list_avg_med_rent = list()
        filters_list_lead_days = ["days_to_rent NOT IN (0,1)"]

        filters_list_yy_differential_1.append(f"c.ticker = '{self.ticker[0].upper()}'" if len(
            self.ticker) == 1 else f"c.ticker in {tuple([ticker.upper() for ticker in self.ticker])}")
        filters_list_yy_differential_2.append(f"c.ticker = '{self.ticker[0].upper()}'" if len(
            self.ticker) == 1 else f"c.ticker in {tuple([ticker.upper() for ticker in self.ticker])}")
        ticker = f"ticker = '{self.ticker[0].upper()}'" if len(
            self.ticker) == 1 else f"ticker in {tuple([ticker.upper() for ticker in self.ticker])}"
        filters_list_avg_med_rent.append(ticker)
        filters_list_lol.append(ticker)  # filter for lol, new rentals, lead days, lease duration
        filters_list_days_market.append(ticker)
        if 'portfolio_year' in filters:
            yy_portfolio_year = (f"c.portfolio_year = {filters['portfolio_year'][0]}" if
                                 len(filters['portfolio_year']) == 1 else
                                 f"c.portfolio_year IN {tuple(filters['portfolio_year'])}")
            filters_list_yy_differential_1.append(yy_portfolio_year)
            filters_list_yy_differential_2.append(yy_portfolio_year)

            filters_list_lol.append(f"portfolio_year = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"portfolio_year IN {tuple(filters['portfolio_year'])}")
            filters_list_days_market.append(f"EXTRACT(YEAR FROM created) = {filters['portfolio_year'][0]}" if
                                            len(filters['portfolio_year']) == 1 else
                                            f"EXTRACT(YEAR FROM created) IN {tuple(filters['portfolio_year'])}")
            filters_list_avg_med_rent.append(f"EXTRACT(YEAR FROM created) = {filters['portfolio_year'][0]}" if
                                             len(filters['portfolio_year']) == 1 else
                                             f"EXTRACT(YEAR FROM created) IN {tuple(filters['portfolio_year'])}")
        if 'msa' in filters:
            yy_msa = (
                f"c.msa = '{filters['msa'][0]}'" if len(filters['msa']) == 1 else f"c.msa IN {tuple(filters['msa'])}")
            filters_list_yy_differential_1.append(yy_msa)
            filters_list_yy_differential_2.append(yy_msa)

            lol_rentals_med_days_msa = (f"msa = '{filters['msa'][0]}'" if
                                        len(filters['msa']) == 1 else
                                        f"msa IN {tuple(filters['msa'])}")
            filters_list_avg_med_rent.append(lol_rentals_med_days_msa)
            filters_list_lol.append(lol_rentals_med_days_msa)
            filters_list_days_market.append(lol_rentals_med_days_msa)
        if 'beds' in filters:
            filters_list_avg_med_rent.append(f"beds = '{filters['beds'][0]}'" if
                                             len(filters['beds']) == 1 else
                                             f"beds IN {tuple([str(bed) for bed in filters['beds']])}")

        filters_list_lead_days.extend(filters_list_lol)
        return {'filters_list_lol': ' AND '.join(filters_list_lol),
                'filters_list_yy_differential_1': ' AND '.join(filters_list_yy_differential_1),
                'filters_list_yy_differential_2': ' AND '.join(filters_list_yy_differential_2),
                'filters_list_avg_med_rent': ' AND '.join(filters_list_avg_med_rent),
                'filters_list_days_market': ' AND '.join(filters_list_days_market),
                'filters_list_lead_days': ' AND '.join(filters_list_lead_days)}


class MultiFamilyCsvMsaSummaryBigQueryViewSet(SingleFamilyCsvMsaSummaryBigQueryViewSet):
    db = 'multi_family'
    app_label = 'multi_family'
    dataset = 'MULTIFAMILY'
    dashboard_perm = 'multi_family.Can view multi_family dashboard'

    def get_query_filters(self, filters):
        filters_list_lol = list()
        filters_list_yy_differential_1 = list()
        filters_list_yy_differential_2 = list()
        filters_list_days_market = list()
        filters_list_avg_med_rent = list()

        filters_list_yy_differential_1.append(f"c.ticker = '{self.ticker[0].upper()}'" if len(
            self.ticker) == 1 else f"c.ticker in {tuple([ticker.upper() for ticker in self.ticker])}")
        filters_list_yy_differential_2.append(f"c.ticker = '{self.ticker[0].upper()}'" if len(
            self.ticker) == 1 else f"c.ticker in {tuple([ticker.upper() for ticker in self.ticker])}")
        ticker = f"ticker = '{self.ticker[0].upper()}'" if len(
            self.ticker) == 1 else f"ticker in {tuple([ticker.upper() for ticker in self.ticker])}"
        filters_list_avg_med_rent.append(ticker)
        filters_list_lol.append(ticker)
        filters_list_days_market.append(ticker)
        if self.start_date:
            self.end_date = self.end_date if self.end_date else date.today()
            filters_list_yy_differential_1.append(f"date BETWEEN "
                                                  f"DATE_SUB(PARSE_DATE('%Y-%m-%d','{self.start_date}'), INTERVAL 1 "
                                                  f"Year) AND "
                                                  f"DATE_SUB(PARSE_DATE('%Y-%m-%d', '{self.end_date}'), INTERVAL 1 Year)")
            filters_list_yy_differential_2.append(f"date BETWEEN "
                                                  f"PARSE_DATE('%Y-%m-%d','{self.start_date}') AND "
                                                  f"PARSE_DATE('%Y-%m-%d', '{self.end_date}')")
            filters_list_avg_med_rent.append(f"date BETWEEN "
                                             f"PARSE_DATE('%Y-%m-%d','{self.start_date}') AND "
                                             f"PARSE_DATE('%Y-%m-%d', '{self.end_date}')")
            filters_list_lol.append(f"date BETWEEN "
                                    f"PARSE_DATE('%Y-%m-%d','{self.start_date}') AND "
                                    f"PARSE_DATE('%Y-%m-%d', '{self.end_date}')")
            filters_list_days_market.append(f"my_date BETWEEN "
                                            f"PARSE_DATE('%Y-%m-%d','{self.start_date}') AND "
                                            f"PARSE_DATE('%Y-%m-%d', '{self.end_date}')")

        portfolio_year = self.portfolio_year if not self.get_same_store_pool_param() else filters['portfolio_year']
        if portfolio_year:
            filters_list_yy_differential_1.append(f"c.portfolio_year = {portfolio_year[0]}" if
                                                  len(portfolio_year) == 1 else
                                                  f"c.portfolio_year IN {tuple(portfolio_year)}")
            filters_list_yy_differential_2.append(f"c.portfolio_year = {portfolio_year[0]}" if
                                                  len(portfolio_year) == 1 else
                                                  f"c.portfolio_year IN {tuple(portfolio_year)}")
            filters_list_avg_med_rent.append(f"EXTRACT(YEAR FROM created) = {portfolio_year[0]}" if
                                             len(portfolio_year) == 1 else
                                             f"EXTRACT(YEAR FROM created) IN {tuple(portfolio_year)}")
            filters_list_lol.append(f"portfolio_year = {portfolio_year[0]}" if
                                    len(portfolio_year) == 1 else
                                    f"portfolio_year IN {tuple(portfolio_year)}")
            filters_list_days_market.append(f"EXTRACT(YEAR FROM created) = {portfolio_year[0]}" if
                                            len(portfolio_year) == 1 else
                                            f"EXTRACT(YEAR FROM created) IN {tuple(portfolio_year)}")

        filters_metric_mapping = {'lol': 'filters_list_lol',
                                  'new_rentals_yoy': 'filters_list_lol',
                                  'new_rentals_yoy2': 'filters_list_lol',
                                  'yy_differential': 'filters_list_yy_differential_2',
                                  'median_rent': 'filters_list_avg_med_rent',
                                  'avg_rent': 'filters_list_avg_med_rent',
                                  'rent_per_square_foot': 'filters_list_avg_med_rent',
                                  'days_market': 'filters_list_days_market'
                                  }

        filters_dict = {'filters_list_lol': ' AND '.join(filters_list_lol),
                        'filters_list_yy_differential_1': ' AND '.join(filters_list_yy_differential_1),
                        'filters_list_yy_differential_2': ' AND '.join(filters_list_yy_differential_2),
                        'filters_list_avg_med_rent': ' AND '.join(filters_list_avg_med_rent),
                        'filters_list_days_market': ' AND '.join(filters_list_days_market)}

        if not self.metric == 'all_metric':
            filters_dict.update({'filters': filters_dict[filters_metric_mapping.get(self.metric)]})

        return filters_dict


class MultiFamilyAssetCountBigQueryViewSet(SingleFamilyAssetCountBigQueryViewSet):
    db = 'multi_family'
    app_label = 'multi_family'
    dataset = 'MULTIFAMILY'
    dashboard_perm = 'multi_family.Can view multi_family dashboard'
