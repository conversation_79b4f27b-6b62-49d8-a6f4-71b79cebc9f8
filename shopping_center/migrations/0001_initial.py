# Generated by Django 3.1.4 on 2022-10-31 15:12

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SCPermissions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'permissions': (('Can view shopping_center dashboard', 'view_shopping_center_dashboard'),
                                ('Can access shopping_center api', 'access_shopping_center_api'),
                                ('Can download shopping_center csv', 'download_shopping_center_csv')),
                'managed': False,
            },
        ),
    ]
