from copy import deepcopy
from rest_framework.status import *
from rest_framework.test import APITestCase
from django.conf import settings


class TestCases(APITestCase):

    @classmethod
    def setUpClass(cls):
        super(TestCases, cls).setUpClass()
        cls.TEST_ADMIN = settings.TEST_ADMIN
        cls.token_creation_url = '/auth/jwt/create/'
        cls.dataset_metrics_filter = '/dashboard/datasets_metrics_filters/?dataset=home_builders'
        cls.data_url_tc = '/home_builders/big_data/?ticker_combine=True'
        cls.data_url_ts = '/home_builders/big_data/?ticker_combine=False'
        cls.data_url_ts_csv = '/home_builders/big_data_csv/?ticker_combine=False'
        cls.request_body = {
            'average_asking_price_non_qmh': {"timeframe": "", "metric": ['average_asking_price_non_qmh'],
                                             "ticker": ["BZH"], "portfolio_year": [2017],
                                             "msa": [], "beds": [2]},
            'average_sold_price_qmh': {"timeframe": "", "metric": ['average_sold_price_qmh'],
                                       "ticker": ["BZH"], "portfolio_year": [2017],
                                       "msa": [], "beds": [2]},
            'yoy_non_qmh': {"timeframe": "", "metric": ['yoy_non_qmh'],
                            "ticker": ["BZH"], "portfolio_year": [2017],
                            "msa": [], "beds": [2]},
            'average_asking_price_qmh': {"timeframe": "", "metric": ['average_asking_price_qmh'],
                                         "ticker": ["BZH"], "portfolio_year": [2017],
                                         "msa": [], "beds": [2]},

        }

    def setUp(self):
        self.set_auth_header(credentials=self.TEST_ADMIN)

    def set_auth_header(self, credentials):
        response = self.client.post(self.token_creation_url, credentials, format='json')
        self.client.credentials(HTTP_AUTHORIZATION='JWT ' + response.data['access'])

    # def test_000_hb_metrics_filter(self):
    #     response = self.client.get(self.dataset_metrics_filter)
    #     self.assertEquals(response.status_code, HTTP_200_OK)

    def test_001_hb_average_asking_price_non_qmh(self):
        request_data = deepcopy(self.request_body)['average_asking_price_non_qmh']
        request_data['timeframe'] = "daily"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "weekly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "monthly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "quarterly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "yearly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        # with msa
        request_data['msa'] = ["Baltimore-Towson, MD MSA"]
        request_data['timeframe'] = "daily"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "weekly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "monthly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "quarterly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "yearly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        # CSV
        request_data['timeframe'] = "daily"
        response = self.client.post(self.data_url_ts_csv, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "weekly"
        response = self.client.post(self.data_url_ts_csv, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "monthly"
        response = self.client.post(self.data_url_ts_csv, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "quarterly"
        response = self.client.post(self.data_url_ts_csv, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "yearly"
        response = self.client.post(self.data_url_ts_csv, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)

    def test_002_hb_average_sold_price_qmh(self):
        request_data = deepcopy(self.request_body)['average_sold_price_qmh']
        request_data['timeframe'] = "daily"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "weekly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "monthly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "quarterly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "yearly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        # with msa
        request_data['msa'] = ["Baltimore-Towson, MD MSA"]
        request_data['timeframe'] = "daily"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "weekly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "monthly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "quarterly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "yearly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        # CSV
        request_data['timeframe'] = "daily"
        response = self.client.post(self.data_url_ts_csv, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "weekly"
        response = self.client.post(self.data_url_ts_csv, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "monthly"
        response = self.client.post(self.data_url_ts_csv, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "quarterly"
        response = self.client.post(self.data_url_ts_csv, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "yearly"
        response = self.client.post(self.data_url_ts_csv, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)

    def test_003_hb_yoy_non_qmh(self):
        request_data = deepcopy(self.request_body)['yoy_non_qmh']
        request_data['timeframe'] = "daily"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "weekly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "monthly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "quarterly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "yearly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        # with msa
        request_data['msa'] = ["Baltimore-Towson, MD MSA"]
        request_data['timeframe'] = "daily"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "weekly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "monthly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "quarterly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "yearly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        # CSV
        request_data['timeframe'] = "daily"
        response = self.client.post(self.data_url_ts_csv, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "weekly"
        response = self.client.post(self.data_url_ts_csv, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "monthly"
        response = self.client.post(self.data_url_ts_csv, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "quarterly"
        response = self.client.post(self.data_url_ts_csv, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "yearly"
        response = self.client.post(self.data_url_ts_csv, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)

    def test_004_hb_dual_metrics(self):
        request_data = deepcopy(self.request_body)['average_asking_price_non_qmh']
        request_data['metric'].append("yoy_non_qmh")
        request_data['timeframe'] = "monthly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        # with msa
        request_data = deepcopy(self.request_body)['average_asking_price_non_qmh']
        request_data['metric'].append("yoy_non_qmh")
        request_data['timeframe'] = "monthly"
        request_data['msa'] = ["Baltimore-Towson, MD MSA"]
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)

    def test_005_hb_ticker_combine(self):
        request_data = deepcopy(self.request_body)['average_asking_price_non_qmh']
        request_data['ticker'].append("DHI")
        request_data['timeframe'] = "monthly"
        response = self.client.post(self.data_url_tc, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        # with msa
        request_data = deepcopy(self.request_body)['average_asking_price_non_qmh']
        request_data['timeframe'] = "monthly"
        request_data['ticker'].append("DHI")
        request_data['msa'] = ["Baltimore-Towson, MD MSA"]
        response = self.client.post(self.data_url_tc, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)

    def test_006_hb_average_asking_price_qmh(self):
        request_data = deepcopy(self.request_body)['average_asking_price_qmh']
        request_data['timeframe'] = "daily"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "weekly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "monthly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "quarterly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "yearly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        # with msa
        request_data['msa'] = ["Baltimore-Towson, MD MSA"]
        request_data['timeframe'] = "daily"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "weekly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "monthly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "quarterly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "yearly"
        response = self.client.post(self.data_url_ts, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        # CSV
        request_data['timeframe'] = "daily"
        response = self.client.post(self.data_url_ts_csv, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "weekly"
        response = self.client.post(self.data_url_ts_csv, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "monthly"
        response = self.client.post(self.data_url_ts_csv, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "quarterly"
        response = self.client.post(self.data_url_ts_csv, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
        request_data['timeframe'] = "yearly"
        response = self.client.post(self.data_url_ts_csv, request_data, format='json')
        self.assertEquals(response.status_code, HTTP_200_OK)
