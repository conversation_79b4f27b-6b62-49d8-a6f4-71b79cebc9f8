#!/usr/bin/env python
"""
Test script for the Enhanced Chatbot
Run this to test the chatbot functionality without starting the Django server
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai.settings')
django.setup()

from chatbot.services.enhanced_chatbot import EnhancedChatbot


def test_chatbot():
    """Test the chatbot with sample queries"""
    
    print("🤖 Enhanced Chatbot Test")
    print("=" * 50)
    
    # Check environment variables
    gemini_key = os.getenv('GEMINI_API_KEY')
    if not gemini_key:
        print("⚠️  Warning: GEMINI_API_KEY not set in environment variables")
        print("   Please set your Gemini API key to test AI functionality")
        return
    
    try:
        # Initialize chatbot
        print("🔧 Initializing chatbot...")
        chatbot = EnhancedChatbot()
        
        # Test system status
        print("\n📊 System Status:")
        status = chatbot.get_system_status()
        for component, is_working in status.items():
            status_icon = "✅" if is_working else "❌"
            print(f"   {status_icon} {component}: {'Working' if is_working else 'Not Working'}")
        
        # Test queries
        test_queries = [
            "How do I add a new property?",  # FAQ query
            "Show me all properties in the database",  # Data query
            "What are the available amenities?",  # FAQ query
            "How many properties do we have?",  # Data query
            "Help me with property setup",  # FAQ query
        ]
        
        print("\n🧪 Testing Queries:")
        print("-" * 30)
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n{i}. Query: {query}")
            try:
                result = chatbot.process_query(query)
                print(f"   Type: {result.get('response_type', 'unknown')}")
                print(f"   Response: {result['response'][:200]}...")
                
                if result.get('sql_query'):
                    print(f"   SQL: {result['sql_query']}")
                
                if result.get('error'):
                    print(f"   ⚠️  Error: {result['error']}")
                    
            except Exception as e:
                print(f"   ❌ Error: {str(e)}")
        
        print("\n✅ Test completed!")
        
    except Exception as e:
        print(f"❌ Failed to initialize chatbot: {str(e)}")
        print("\nTroubleshooting:")
        print("1. Make sure GEMINI_API_KEY is set in your environment")
        print("2. Check database connection settings")
        print("3. Ensure all dependencies are installed")


def interactive_test():
    """Interactive chatbot testing"""
    
    print("\n🎯 Interactive Chatbot Test")
    print("Type 'quit' to exit")
    print("-" * 30)
    
    try:
        chatbot = EnhancedChatbot()
        
        while True:
            query = input("\n💬 You: ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not query:
                continue
            
            try:
                result = chatbot.process_query(query)
                print(f"🤖 Bot: {result['response']}")
                
                # Show additional info if available
                if result.get('sql_query'):
                    print(f"📊 SQL: {result['sql_query']}")
                
                if result.get('data_results') and len(result['data_results']) > 0:
                    print(f"📈 Found {len(result['data_results'])} results")
                
            except Exception as e:
                print(f"❌ Error: {str(e)}")
                
    except Exception as e:
        print(f"❌ Failed to start interactive test: {str(e)}")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Test the Enhanced Chatbot')
    parser.add_argument('--interactive', '-i', action='store_true', 
                       help='Run interactive test mode')
    
    args = parser.parse_args()
    
    if args.interactive:
        interactive_test()
    else:
        test_chatbot()
