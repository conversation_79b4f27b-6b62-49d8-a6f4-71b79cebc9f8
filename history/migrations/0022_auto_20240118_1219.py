# Generated by Django 3.2.2 on 2024-01-18 07:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('history', '0021_usercsvdownloaddetailedhistory'),
    ]

    operations = [
        migrations.RenameField(
            model_name='usercsvdownloaddetailedhistory',
            old_name='type',
            new_name='insights_type',
        ),
        migrations.AddField(
            model_name='userdashboardaccesshistory',
            name='insights_type',
            field=models.<PERSON>r<PERSON>ield(default=None, max_length=100),
        ),
        migrations.AddField(
            model_name='userdashboardaccesshistory',
            name='metrics',
            field=models.<PERSON><PERSON><PERSON><PERSON>(default=None, max_length=100),
        ),
        migrations.AddField(
            model_name='userdashboardaccesshistory',
            name='tickers',
            field=models.CharField(default=None, max_length=100),
        ),
        migrations.AddField(
            model_name='userdashboardaccesshistory',
            name='timeframe',
            field=models.Cha<PERSON><PERSON><PERSON>(default=None, max_length=100),
        ),
    ]
