from django.db import models


class ManufacturedHousingPermissions(models.Model):
    class Meta:
        managed = False
        permissions = (
            ("Can view manufactured_housing dashboard", 'view_manufactured_housing_dashboard'),
            ("Can access manufactured_housing api", 'access_manufactured_housing_api'),
            ("Can download manufactured_housing csv", 'download_manufactured_housing_csv'),
            ("Can access manufactured_housing bucket", 'client_manufactured_housing'),
            ("Can download manufactured_housing report", 'download_manufactured_housing_report'),
            ("Can download manufactured_housing daily csv", 'download_manufactured_housing_daily_csv'),
            ("Can download manufactured_housing daily msa csv", 'download_manufactured_housing_daily_msa_csv')
        )
