from datetime import date
from google.cloud import bigquery
from user_authentication.utils import GenericAPIException
from data_portal.api.views import (DataPortalViewSet, DataPortalCsvViewSet, DataPortalRelativeAvgViewSet,
                                   DataPortalMsaSummaryViewSet, DataPortalAllMetricCsvViewSet,
                                   DataPortalMsaSummaryCsvViewSet, DataPortalAssetCountViewSet)
from data_portal.api.utils import (model_serializer_mapper, csv_model_serializer_mapper,
                                   relative_avg_model_serializer_mapper, csv_relative_avg_model_serializer_mapper,
                                   msa_summary_model_serializer_mapper, ticker_default_portfolio_years,
                                   asset_count_model_serializer_mapper)
from data_portal.api.serializers import ValidateFilters, ValidateRelativeAvgFilters
from history.models import UserSameStorePoolPF


class SingleFamilyBigQueryViewSet(DataPortalViewSet):
    db = 'single_family'
    app_label = 'single_family'
    dataset = 'SINGLEFAMILY'
    dashboard_perm = 'single_family.Can view single_family dashboard'
    gcp_project = 'webdata-207211'

    def get_query_filters(self, filters):
        if self.metric == 'yy_differential':
            filters_list = [f"c.ticker = '{self.ticker[0].upper()}'" if len(
                self.ticker) == 1 else f"c.ticker in {tuple([ticker.upper() for ticker in self.ticker])}"]
            if 'portfolio_year' in filters:
                filters_list.append(f"c.portfolio_year = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"c.portfolio_year IN {tuple(filters['portfolio_year'])}")
            if 'msa' in filters:
                filters_list.append(f"c.msa = '{filters['msa'][0]}'" if
                                    len(filters['msa']) == 1 else
                                    f"c.msa IN {tuple(filters['msa'])}")
        else:
            filters_list = [f"ticker = '{self.ticker[0].upper()}'" if len(
                self.ticker) == 1 else f"ticker in {tuple([ticker.upper() for ticker in self.ticker])}"]
        if self.metric in ['avg_rent', 'median_rent', 'rent_per_square_foot']:
            if 'portfolio_year' in filters:
                filters_list.append(f"EXTRACT(YEAR FROM created) = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"EXTRACT(YEAR FROM created) IN {tuple(filters['portfolio_year'])}")
            if 'msa' in filters:
                filters_list.append(f"msa = '{filters['msa'][0]}'" if
                                    len(filters['msa']) == 1 else
                                    f"msa IN {tuple(filters['msa'])}")
            if 'beds' in filters:
                filters_list.append(f"beds = '{filters['beds'][0]}'" if
                                    len(filters['beds']) == 1 else
                                    f"beds IN {tuple([str(bed) for bed in filters['beds']])}")
        elif self.metric in ['lol', 'new_rentals_yoy', 'new_rentals_yoy2', 'median_lease_duration']:
            if 'portfolio_year' in filters:
                filters_list.append(f"portfolio_year = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"portfolio_year IN {tuple(filters['portfolio_year'])}")
            if 'msa' in filters:
                filters_list.append(f"msa = '{filters['msa'][0]}'" if
                                    len(filters['msa']) == 1 else
                                    f"msa IN {tuple(filters['msa'])}")
        elif self.metric == 'days_market':
            if 'portfolio_year' in filters:
                filters_list.append(f"year = '{filters['portfolio_year'][0]}'" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"year IN {tuple([str(fl) for fl in filters['portfolio_year']])}")
            if 'msa' in filters:
                filters_list.append(f"msa = '{filters['msa'][0]}'" if
                                    len(filters['msa']) == 1 else
                                    f"msa IN {tuple(filters['msa'])}")

        elif self.metric in ['relative_new_rentals_avg']:
            if 'portfolio_year' in filters:
                filters_list.append(f"portfolio_year = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"portfolio_year IN {tuple(filters['portfolio_year'])}")
            if 'beds' in filters:
                filters_list.append(f"beds = '{filters['beds'][0]}'" if
                                    len(filters['beds']) == 1 else
                                    f"beds IN {tuple([str(bed) for bed in filters['beds']])}")

        elif self.metric in ['median_lead_days']:
            if 'portfolio_year' in filters:
                filters_list.append(f"portfolio_year = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"portfolio_year IN {tuple(filters['portfolio_year'])}")

        filters = ' AND '.join(filters_list)
        return {'filters': filters}

    def get_serializer_class(self):
        self.model_serializer_mapping = model_serializer_mapper[self.dataset][self.msa]
        self.serializer_class = self.model_serializer_mapping[self.metric]['serializer']
        return self.serializer_class

    def get_queryset(self, filters):
        if self.get_same_store_pool_param():
            filters = self.get_same_store_pool_filters(filters)
        else:
            filters = self.get_query_filters(filters)
        try:
            self.model_serializer_mapping = model_serializer_mapper[self.dataset][self.msa]
            query = self.model_serializer_mapping[self.metric]['queryset'][self.timeframe]
            self.query = query.format(**filters)
        except KeyError:
            raise KeyError
        try:
            client = bigquery.Client(project=self.gcp_project)
            self.queryset = client.query(self.query).result()
        except Exception as e:
            raise Exception(str(e))
        return self.queryset

    def get_same_store_pool_filters(self, filters):
        original_tickers = self.ticker
        dataset = self.dataset.lower().replace(' ', '_')
        saved_values = list(UserSameStorePoolPF.objects.filter(user=self.request.user).values(dataset))
        if not saved_values:
            saved_values = [{dataset: ticker_default_portfolio_years.get(self.dataset.upper())}]
        store_pool_filters = list()
        store_pool = filters
        for ticker in original_tickers:
            self.ticker = [ticker]
            try:
                store_pool['portfolio_year'] = saved_values[0][dataset][ticker.upper()]
            except:
                store_pool['portfolio_year'] = ticker_default_portfolio_years.get(self.dataset.upper())[ticker.upper()]
            store_pool_filters.append(f"({self.get_query_filters(store_pool).get('filters')})")
        filters = f"({' OR '.join(store_pool_filters)})"
        self.ticker = original_tickers
        return {'filters': filters}


class SingleFamilyCsvBigQueryViewSet(DataPortalCsvViewSet):
    db = 'single_family'
    app_label = 'single_family'
    dataset = 'SINGLEFAMILY'
    dashboard_perm = 'single_family.Can download single_family csv'
    gcp_project = 'webdata-207211'

    def get_query_filters(self, filters):
        if self.metric == 'yy_differential':
            filters_list = [f"c.ticker = '{self.ticker[0].upper()}'" if len(
                self.ticker) == 1 else f"c.ticker in {tuple([ticker.upper() for ticker in self.ticker])}"]
            if 'portfolio_year' in filters:
                filters_list.append(f"c.portfolio_year = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"c.portfolio_year IN {tuple(filters['portfolio_year'])}")
            if 'msa' in filters:
                filters_list.append(f"c.msa = '{filters['msa'][0]}'" if
                                    len(filters['msa']) == 1 else
                                    f"c.msa IN {tuple(filters['msa'])}")
        else:
            filters_list = [f"ticker = '{self.ticker[0].upper()}'" if len(
                self.ticker) == 1 else f"ticker in {tuple([ticker.upper() for ticker in self.ticker])}"]
        if self.metric in ['avg_rent', 'median_rent', 'rent_per_square_foot']:
            if 'portfolio_year' in filters:
                filters_list.append(f"EXTRACT(YEAR FROM created) = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"EXTRACT(YEAR FROM created) IN {tuple(filters['portfolio_year'])}")
            if 'msa' in filters:
                filters_list.append(f"msa = '{filters['msa'][0]}'" if
                                    len(filters['msa']) == 1 else
                                    f"msa IN {tuple(filters['msa'])}")
            if 'beds' in filters:
                filters_list.append(f"beds = '{filters['beds'][0]}'" if
                                    len(filters['beds']) == 1 else
                                    f"beds IN {tuple([str(bed) for bed in filters['beds']])}")
        elif self.metric in ['lol', 'new_rentals_yoy', 'new_rentals_yoy2', 'median_lease_duration']:
            if 'portfolio_year' in filters:
                filters_list.append(f"portfolio_year = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"portfolio_year IN {tuple(filters['portfolio_year'])}")
            if 'msa' in filters:
                filters_list.append(f"msa = '{filters['msa'][0]}'" if
                                    len(filters['msa']) == 1 else
                                    f"msa IN {tuple(filters['msa'])}")
        elif self.metric == 'days_market':
            if 'portfolio_year' in filters:
                filters_list.append(f"year = '{filters['portfolio_year'][0]}'" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"year IN {tuple([str(fl) for fl in filters['portfolio_year']])}")
            if 'msa' in filters:
                filters_list.append(f"msa = '{filters['msa'][0]}'" if
                                    len(filters['msa']) == 1 else
                                    f"msa IN {tuple(filters['msa'])}")

        elif self.metric in ['relative_new_rentals_avg']:
            if 'portfolio_year' in filters:
                filters_list.append(f"portfolio_year = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"portfolio_year IN {tuple(filters['portfolio_year'])}")
            if 'beds' in filters:
                filters_list.append(f"beds = '{filters['beds'][0]}'" if
                                    len(filters['beds']) == 1 else
                                    f"beds IN {tuple([str(bed) for bed in filters['beds']])}")

        elif self.metric in ['median_lead_days']:
            if 'portfolio_year' in filters:
                filters_list.append(f"portfolio_year = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"portfolio_year IN {tuple(filters['portfolio_year'])}")

        filters = ' AND '.join(filters_list)
        return {'filters': filters}

    def get_queryset(self, filters):
        if self.get_same_store_pool_param():
            filters = self.get_same_store_pool_filters(filters)
        else:
            filters = self.get_query_filters(filters)
        try:
            self.model_serializer_mapping = model_serializer_mapper[self.dataset][self.msa]
            query = self.model_serializer_mapping[self.metric]['queryset'][self.timeframe]
            self.query = query.format(**filters)
        except KeyError:
            raise KeyError
        try:
            client = bigquery.Client(project=self.gcp_project)
            self.queryset = client.query(self.query).result()
        except Exception as e:
            raise Exception(str(e))
        return self.queryset.to_dataframe()

    def get_same_store_pool_filters(self, filters):
        original_tickers = self.ticker
        dataset = self.dataset.lower().replace(' ', '_')
        saved_values = list(UserSameStorePoolPF.objects.filter(user=self.request.user).values(dataset))
        if not saved_values:
            saved_values = [{dataset: ticker_default_portfolio_years.get(self.dataset.upper())}]
        store_pool_filters = list()
        store_pool = filters
        for ticker in original_tickers:
            self.ticker = [ticker]
            try:
                store_pool['portfolio_year'] = saved_values[0][dataset][ticker.upper()]
            except:
                store_pool['portfolio_year'] = ticker_default_portfolio_years.get(self.dataset.upper())[ticker.upper()]
            store_pool_filters.append(f"({self.get_query_filters(store_pool).get('filters')})")
        filters = f"({' OR '.join(store_pool_filters)})"
        self.ticker = original_tickers
        return {'filters': filters}


class SingleFamilyRelativeAvgBigQueryViewSet(DataPortalRelativeAvgViewSet):
    db = 'single_family'
    app_label = 'single_family'
    dataset = 'SINGLEFAMILY'
    dashboard_perm = 'single_family.Can view single_family dashboard'
    gcp_project = 'webdata-207211'

    def get_filters(self):
        filters = ValidateRelativeAvgFilters(data=self.request.data, context={'dataset': self.dataset,
                                                                              'ticker': self.ticker,
                                                                              'metric': self.metric,
                                                                              'date_type': self.date_type},
                                             partial=True)
        filters.is_valid(raise_exception=True)
        filters = filters.validated_data
        return filters

    def get_query_filters(self, filters):
        filters_list = [f"ticker = '{self.ticker[0].upper()}'" if len(
            self.ticker) == 1 else f"ticker in {tuple([ticker.upper() for ticker in self.ticker])}"]
        if 'portfolio_year' in filters:
            filters_list.append(f"portfolio_year = {filters['portfolio_year'][0]}" if
                                len(filters['portfolio_year']) == 1 else
                                f"portfolio_year IN {tuple(filters['portfolio_year'])}")
        if 'beds' in filters:
            filters_list.append(f"beds = '{filters['beds'][0]}'" if
                                len(filters['beds']) == 1 else
                                f"beds IN {tuple([str(bed) for bed in filters['beds']])}")

        if 'base_date' in filters:
            filter_1 = [f"CONCAT(year_val, '-', FORMAT('%02d', month_val)) = '{filters['base_date']}'"]
            filter_1.extend(filters_list)
            filter_2 = [
                f"PARSE_DATE('%Y-%m', CONCAT(year_val, '-', month_val)) >= PARSE_DATE('%Y-%m', '{filters['base_date']}')"]
            filter_2.extend(filters_list)
        else:
            raise GenericAPIException("Please send base_date")

        filters_1 = ' AND '.join(filter_1)
        filters_2 = ' AND '.join(filter_2)
        return {'filters_1': filters_1, 'filters_2': filters_2}

    def get_same_store_pool_filters(self, filters):
        original_tickers = self.ticker
        dataset = self.dataset.lower().replace(' ', '_')
        saved_values = list(UserSameStorePoolPF.objects.filter(user=self.request.user).values(dataset))
        if not saved_values:
            saved_values = [{dataset: ticker_default_portfolio_years.get(self.dataset.upper())}]
        store_pool_filters = list()
        store_pool = filters
        for ticker in original_tickers:
            self.ticker = [ticker]
            try:
                store_pool['portfolio_year'] = saved_values[0][dataset][ticker.upper()]
            except:
                store_pool['portfolio_year'] = ticker_default_portfolio_years.get(self.dataset.upper())[ticker.upper()]
            store_pool_filters.append(f"({self.get_query_filters(store_pool).get('filters')})")
        filters = f"({' OR '.join(store_pool_filters)})"
        self.ticker = original_tickers
        return {'filters': filters}

    def get_serializer_class(self):
        self.model_serializer_mapping = relative_avg_model_serializer_mapper[self.dataset]
        self.serializer_class = self.model_serializer_mapping[self.metric]['serializer']
        return self.serializer_class

    def get_queryset(self, filters):
        if self.get_same_store_pool_param():
            filters = self.get_same_store_pool_filters(filters)
        else:
            filters = self.get_query_filters(filters)
        try:
            self.model_serializer_mapping = relative_avg_model_serializer_mapper[self.dataset]
            query = self.model_serializer_mapping[self.metric]['queryset'][self.timeframe][self.date_type][
                self.timeframe]
            self.query = query.format(**filters)
        except KeyError:
            raise KeyError
        try:
            client = bigquery.Client(project=self.gcp_project)
            self.queryset = client.query(self.query).result()
        except Exception as e:
            raise Exception(str(e))
        if self.csv:
            return self.queryset.to_dataframe()
        else:
            return self.queryset


class SingleFamilyRelativeAverageBigQueryViewSet(SingleFamilyRelativeAvgBigQueryViewSet):
    db = 'single_family'
    app_label = 'single_family'
    dataset = 'SINGLEFAMILY'
    dashboard_perm = 'single_family.Can view single_family dashboard'
    gcp_project = 'webdata-207211'

    def get_query_filters(self, filters):
        filters_list = [f"ticker = '{self.ticker[0].upper()}'" if len(
            self.ticker) == 1 else f"ticker in {tuple([ticker.upper() for ticker in self.ticker])}"]
        if 'portfolio_year' in filters:
            filters_list.append(f"portfolio_year = {filters['portfolio_year'][0]}" if
                                len(filters['portfolio_year']) == 1 else
                                f"portfolio_year IN {tuple(filters['portfolio_year'])}")
        if 'beds' in filters:
            filters_list.append(f"beds = '{filters['beds'][0]}'" if
                                len(filters['beds']) == 1 else
                                f"beds IN {tuple([str(bed) for bed in filters['beds']])}")

        filters = ' AND '.join(filters_list)
        return {'filters': filters}


class SingleFamilyMsaSummaryBigQueryViewSet(DataPortalMsaSummaryViewSet):
    db = 'single_family'
    app_label = 'single_family'
    dataset = 'SINGLEFAMILY'
    dashboard_perm = 'single_family.Can view single_family dashboard'
    gcp_project = 'webdata-207211'

    def get_query_filters(self, filters):
        date_range, date_range_yy = self.get_date_range()
        filters_list_yy_differential_1 = list()
        filters_list = [f"ticker = '{self.ticker[0].upper()}'" if len(
            self.ticker) == 1 else f"ticker in {tuple([ticker.upper() for ticker in self.ticker])}"]
        if self.metric == 'yy_differential':
            yy_ticker = (f"c.ticker = '{self.ticker[0].upper()}'" if len(
                self.ticker) == 1 else f"c.ticker in {tuple([ticker.upper() for ticker in self.ticker])}")
            filters_list.append(yy_ticker)
            filters_list_yy_differential_1.append(yy_ticker)
        if 'unit_area' in filters:
            filters_list.append(f"unit_area = '{filters['unit_area']}'")
        if 'portfolio_year' in filters:
            if self.metric == 'yy_differential':
                yy_portfolio = (f"c.portfolio_year = {filters['portfolio_year'][0]}" if
                                len(filters['portfolio_year']) == 1 else
                                f"c.portfolio_year IN {tuple(filters['portfolio_year'])}")
                filters_list.append(yy_portfolio)
                filters_list_yy_differential_1.append(yy_portfolio)
            elif self.metric in ['avg_rent', 'median_rent', 'rent_per_square_foot']:
                filters_list.append(f"EXTRACT(YEAR FROM created) = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"EXTRACT(YEAR FROM created) IN {tuple(filters['portfolio_year'])}")
            elif self.metric in ['lol', 'new_rentals_yoy', 'new_rentals_yoy2']:
                filters_list.append(f"portfolio_year = {filters['portfolio_year'][0]}" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"portfolio_year IN {tuple(filters['portfolio_year'])}")
            elif self.metric == 'days_market':
                filters_list.append(f"year = '{filters['portfolio_year'][0]}'" if
                                    len(filters['portfolio_year']) == 1 else
                                    f"year IN {tuple([str(fl) for fl in filters['portfolio_year']])}")

        filters_list.extend(date_range)
        filters_list_yy_differential_1.extend(date_range_yy)
        filters = ' AND '.join(filters_list)
        filters_list_yy_differential_1 = ' AND '.join(filters_list_yy_differential_1)
        return {'filters': filters, "filters_list_yy_differential_1": filters_list_yy_differential_1}

    def get_date_range(self):
        filters_list = list()
        filters_list_yy_differential_1 = list()  # yy_diff is difference of same month and 2 years
        if self.start_date:
            self.end_date = self.end_date if self.end_date else date.today()
            if self.metric in ['days_market']:
                filters_list.append(f"my_date BETWEEN "
                                    f"PARSE_DATE('%Y-%m-%d','{self.start_date}') AND "
                                    f"PARSE_DATE('%Y-%m-%d', '{self.end_date}')")
            else:
                filters_list.append(f"date BETWEEN "
                                    f"PARSE_DATE('%Y-%m-%d','{self.start_date}') AND "
                                    f"PARSE_DATE('%Y-%m-%d', '{self.end_date}')")
                if self.metric in ['yy_differential']:
                    filters_list_yy_differential_1.append(f"date BETWEEN "
                                                          f"DATE_SUB(PARSE_DATE('%Y-%m-%d','{self.start_date}'), INTERVAL 1 "
                                                          f"Year) AND "
                                                          f"DATE_SUB(PARSE_DATE('%Y-%m-%d', '{self.end_date}'), INTERVAL 1 Year)")
        return filters_list, filters_list_yy_differential_1

    def get_queryset(self, filters):
        if self.get_same_store_pool_param():
            filters = self.get_same_store_pool_filters(filters)
        else:
            filters = self.get_query_filters(filters)
        try:
            self.model_serializer_mapping = msa_summary_model_serializer_mapper[self.dataset][self.msa]
            query = self.model_serializer_mapping[self.metric]['queryset'][self.timeframe]
            query = query.format(**filters)
        except KeyError:
            raise KeyError
        try:
            client = bigquery.Client(project=self.gcp_project)
            self.queryset = client.query(query).result()
        except Exception as e:
            raise Exception(str(e))
        return self.queryset.to_dataframe()

    def get_same_store_pool_filters(self, filters):
        original_tickers = self.ticker
        dataset = self.dataset.lower().replace(' ', '_')
        saved_values = list(UserSameStorePoolPF.objects.filter(user=self.request.user).values(dataset))
        if not saved_values:
            saved_values = [{dataset: ticker_default_portfolio_years.get(self.dataset.upper())}]
        store_pool_filters = list()
        store_pool = filters
        for ticker in original_tickers:
            self.ticker = [ticker]
            try:
                store_pool['portfolio_year'] = saved_values[0][dataset][ticker.upper()]
            except:
                store_pool['portfolio_year'] = ticker_default_portfolio_years.get(self.dataset.upper())[ticker.upper()]
            store_pool_filters.append(f"({self.get_query_filters(store_pool).get('filters')})")
        filters = f"({' OR '.join(store_pool_filters)})"
        self.ticker = original_tickers
        return {'filters': filters,
                "filters_list_yy_differential_1": self.get_query_filters(store_pool).get(
                    'filters_list_yy_differential_1')}


class SingleFamilyAllMetricsCsvBigQueryViewSet(DataPortalAllMetricCsvViewSet):
    db = 'single_family'
    app_label = 'single_family'
    dataset = 'SINGLEFAMILY'
    dashboard_perm = 'single_family.Can download single_family csv'


class SingleFamilyCsvMsaSummaryBigQueryViewSet(DataPortalMsaSummaryCsvViewSet):
    db = 'single_family'
    app_label = 'single_family'
    dataset = 'SINGLEFAMILY'
    dashboard_perm = 'single_family.Can download single_family csv'


class SingleFamilyAssetCountBigQueryViewSet(DataPortalAssetCountViewSet):
    db = 'single_family'
    app_label = 'single_family'
    dataset = 'SINGLEFAMILY'
    dashboard_perm = 'single_family.Can view single_family dashboard'
    gcp_project = 'webdata-207211'

    def get_query_filters(self):
        filters_list = [f"ticker = '{self.ticker[0].upper()}'" if len(
            self.ticker) == 1 else f"ticker in {tuple([ticker.upper() for ticker in self.ticker])}"]
        filters = ' AND '.join(filters_list)
        return {'filters': filters}

    def get_queryset(self, filters):
        filters = self.get_query_filters()
        try:
            self.model_serializer_mapping = asset_count_model_serializer_mapper[self.dataset][self.msa]
            query = self.model_serializer_mapping[self.metric]['queryset']['yearly']
            query = query.format(**filters)
        except KeyError:
            raise KeyError
        try:
            client = bigquery.Client(project=self.gcp_project)
            self.queryset = client.query(query).result()
        except Exception as e:
            raise Exception(str(e))
        return self.queryset.to_dataframe()
