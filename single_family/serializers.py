import statistics
from collections import defaultdict
from natsort import natsorted
from rest_framework import serializers
from orjson import loads


class Serializer(serializers.ModelSerializer):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.context['view'].msa == 'msa':
            self.fields['msa'] = serializers.CharField(max_length=250)


class DictSerializer(serializers.ListSerializer):
    """
    Customizing default list serializer data for combining two metrics data in renderer or combining tickers data
    in case of ticker combine.
    List of object instances -> dict of dicts of primitive datatypes.
    """

    @property
    def data(self):
        data = super(serializers.ListSerializer, self).data
        return data

    def to_representation(self, iterable):
        if self.context['ticker_combine']:
            data_dict, min_max = defaultdict(dict), set()
            asset_col = self.child.get_asset_col()
            for item in iterable:
                item = loads(item[0])
                item['date'] = item.pop('dated', None) if 'dated' in item else item.pop('date', None)
                min_max.add(item[self.context['metric']]) if item[self.context['metric']] is not None else None
                item[f'{item["ticker"].lower()}__{self.context["metric"]}'] = item.pop(self.context['metric'])
                item[f'{item.pop("ticker").lower()}__{asset_col}'] = item.pop(asset_col)
                data_dict[item['date']].update(item)
            return data_dict, {'min': round(min(min_max), 3) if min_max else None,
                               'max': round(max(min_max), 3) if min_max else None}
        elif self.context['single_metric']:
            data_dict = defaultdict(list)
            for item in iterable:
                item = loads(item[0])
                item['date'] = item.pop('dated', None) if 'dated' in item else item.pop('date', None)
                data_dict[item['ticker'].lower()].append(item)
            return data_dict
        else:
            data_dict = defaultdict(dict, {ticker: defaultdict(dict) for ticker in self.context['tickers']})
            for item in iterable:
                item = loads(item[0])
                item['date'] = item.pop('dated', None) if 'dated' in item else item.pop('date', None)
                data_dict[item['ticker'].lower()][f"{item['date']}__{item['ticker'].lower()}"] = item
            return data_dict


class RentalsRelativeAvgDictSerializer(DictSerializer):

    def to_representation(self, iterable):
        if self.context['ticker_combine']:
            data_dict, min_max = defaultdict(dict), set()
            asset_col = self.child.get_asset_col()
            rent_col = self.child.get_rent_col()
            base_col = self.child.get_base_avg_col()
            for item in iterable:
                item = loads(item[0])
                item['date'] = item.pop('dated', None) if 'dated' in item else item.pop('date', None)
                min_max.add(item[self.context['metric']]) if item[self.context['metric']] is not None else None
                item[f'{item["ticker"].lower()}__{self.context["metric"]}'] = item.pop(self.context['metric'])
                item[f'{item["ticker"].lower()}__{asset_col}'] = item.pop(asset_col)
                item[f'{item["ticker"].lower()}__{rent_col}'] = item.pop(rent_col)
                item[f'{item.pop("ticker").lower()}__{base_col}'] = item.pop(base_col)
                data_dict[item['date']].update(item)
            return data_dict, {'min': round(min(min_max), 3) if min_max else None,
                               'max': round(max(min_max), 3) if min_max else None}
        elif self.context['single_metric']:
            data_dict = defaultdict(list)
            for item in iterable:
                item = loads(item[0])
                item['date'] = item.pop('dated', None) if 'dated' in item else item.pop('date', None)
                data_dict[item['ticker'].lower()].append(item)
            return data_dict
        else:
            data_dict = defaultdict(dict, {ticker: defaultdict(dict) for ticker in self.context['tickers']})
            for item in iterable:
                item = loads(item[0])
                item['date'] = item.pop('dated', None) if 'dated' in item else item.pop('date', None)
                data_dict[item['ticker'].lower()][f"{item['date']}__{item['ticker'].lower()}"] = item
            return data_dict


class MsaDictSerializer(serializers.ListSerializer):
    """
    Customizing default list serializer data for combining two metrics data for multiple msa in renderer.
    List of object instances -> dict of dicts of primitive datatypes.
    """

    @property
    def data(self):
        data = super(serializers.ListSerializer, self).data
        return data

    def to_representation(self, iterable):
        asset_col = self.child.get_asset_col()
        if not self.context.get('dual_metrics'):
            if self.context['ticker_combine']:
                data_dict = defaultdict(dict, {msa: defaultdict(dict) for msa in self.context['all_msa']})
                for item in iterable:
                    item = loads(item[0])
                    item['date'] = item.pop('dated', None) if 'dated' in item else item.pop('date', None)
                    item[f'{item["ticker"].lower()}__{item["msa"].lower()}__{self.context["metric"]}'] = item.pop(
                        self.context['metric'])
                    item[f'{item.pop("ticker").lower()}__{item["msa"].lower()}__{asset_col}'] = item.pop(asset_col)
                    data_dict[item.pop('msa')][f"{item['date']}"].update(item)
                return data_dict
            elif self.context['single_metric']:
                data_dict = defaultdict(dict, {ticker: defaultdict(dict) for ticker in self.context['tickers']})
                for item in iterable:
                    item = loads(item[0])
                    item['date'] = item.pop('dated', None) if 'dated' in item else item.pop('date', None)
                    item[f'{item["ticker"].lower()}__{item["msa"].lower()}__{self.context["metric"]}'] = item.pop(
                        self.context['metric'])
                    item[f'{item["ticker"].lower()}__{item.pop("msa").lower()}__{asset_col}'] = item.pop(asset_col)
                    data_dict[item.pop('ticker').lower()][f"{item['date']}"].update(item)
                return data_dict
            else:
                data_dict = defaultdict(dict, {ticker: defaultdict(dict) for ticker in self.context['tickers']})
                for item in iterable:
                    item = loads(item[0])
                    item['date'] = item.pop('dated', None) if 'dated' in item else item.pop('date', None)
                    item[f'{item["ticker"].lower()}__{item["msa"].lower()}__{self.context["metric"]}'] = item.pop(
                        self.context['metric'])
                    item[f'{item["ticker"].lower()}__{item.pop("msa").lower()}__{asset_col}'] = item.pop(asset_col)
                    data_dict[item.pop('ticker').lower()][f"{item['date']}"].update(item)
                return data_dict
        else:
            if not self.context.get('asset_col'):
                data_dict = defaultdict(dict, {ticker: defaultdict(dict) for ticker in self.context['tickers']})
                for item in iterable:
                    item = loads(item[0])
                    item['date'] = item.pop('dated', None) if 'dated' in item else item.pop('date', None)
                    item[
                        f'{item["ticker"].lower()}__{item["msa"].lower()}__{self.context["dual_metrics"][0]}'] = item.pop(
                        self.context['dual_metrics'][0])
                    item[
                        f'{item["ticker"].lower()}__{item["msa"].lower()}__{self.context["dual_metrics"][-1]}'] = item.pop(
                        self.context['dual_metrics'][-1])
                    item[f'{item["ticker"].lower()}__{item.pop("msa").lower()}__{asset_col}'] = item.pop(asset_col)
                    data_dict[item.pop('ticker').lower()][f"{item['date']}"].update(item)
                return data_dict
            else:
                data_dict = defaultdict(dict, {ticker: defaultdict(dict) for ticker in self.context['tickers']})
                for item in iterable:
                    item = loads(item[0])
                    item['date'] = item.pop('dated', None) if 'dated' in item else item.pop('date', None)
                    item[
                        f'{item["ticker"].lower()}__{item["msa"].lower()}__{self.context["dual_metrics"][0]}'] = item.pop(
                        self.context['dual_metrics'][0])
                    item[
                        f'{item["ticker"].lower()}__{item["msa"].lower()}__{self.context["dual_metrics"][-1]}'] = item.pop(
                        self.context['dual_metrics'][-1])
                    item[f'{item["ticker"].lower()}__{item["msa"].lower()}__{self.context["asset_col"][0]}'] = item.pop(
                        self.context['asset_col'][0])
                    if self.context['metric'] not in ['median_10x10_web_vs_walkin']:
                        item[
                            f'{item["ticker"].lower()}__{item.pop("msa").lower()}__{self.context["asset_col"][-1]}'] = item.pop(
                            self.context['asset_col'][-1])
                    data_dict[item.pop('ticker').lower()][f"{item['date']}"].update(item)
                return data_dict


class TickerCombineSerializer(serializers.Serializer):
    """
    Customizing default serializer data field names for ticker combine
    """

    @staticmethod
    def get_combined_cols():
        """
        must override by child class
        """
        return []

    def ticker_combine(self, data):
        for col in self.get_combined_cols():
            data[f'{data["ticker"].lower()}__{col}'] = data.get(col) if col == self.context[
                'metric'] else data.pop(col, None)

    def to_representation(self, instance):
        data = super().to_representation(instance=instance)
        if self.context.get('ticker_combine'):
            self.ticker_combine(data)
        return data


class SFAptsDaysOnMarketBigQuerySerializer(serializers.Serializer):

    @staticmethod
    def get_asset_col():
        return 'apt_counts'

    class Meta:
        list_serializer_class = DictSerializer


class SFMedianLeaseDurationBigQuerySerializer(serializers.Serializer):

    @staticmethod
    def get_asset_col():
        return 'med_records'

    class Meta:
        list_serializer_class = DictSerializer


class SFAptsDaysOnMarketMSABigQuerySerializer(serializers.Serializer):

    @staticmethod
    def get_asset_col():
        return 'apt_counts'

    class Meta:
        list_serializer_class = MsaDictSerializer


class SFMedianRentBigQuerySerializer(serializers.Serializer):

    @staticmethod
    def get_asset_col():
        return 'med_records'

    class Meta:
        list_serializer_class = DictSerializer


class SFMedianRentMSABigQuerySerializer(serializers.Serializer):

    @staticmethod
    def get_asset_col():
        return 'med_records'

    class Meta:
        list_serializer_class = MsaDictSerializer


class SFAverageRentBigQuerySerializer(serializers.Serializer):

    @staticmethod
    def get_asset_col():
        return 'avg_records'

    class Meta:
        list_serializer_class = DictSerializer


class SFRentPerSqftBigQuerySerializer(serializers.Serializer):

    @staticmethod
    def get_asset_col():
        return 'sqf_records'

    class Meta:
        list_serializer_class = DictSerializer


class SFAverageRentMSABigQuerySerializer(serializers.Serializer):

    @staticmethod
    def get_asset_col():
        return 'avg_records'

    class Meta:
        list_serializer_class = MsaDictSerializer


class SFRentPerSqftMSABigQuerySerializer(serializers.Serializer):

    @staticmethod
    def get_asset_col():
        return 'sqf_records'

    class Meta:
        list_serializer_class = MsaDictSerializer


class SFLOLBigQuerySerializer(serializers.Serializer):

    @staticmethod
    def get_asset_col():
        return 'units'

    class Meta:
        list_serializer_class = DictSerializer


class SFLOLMSABigQuerySerializer(serializers.Serializer):

    @staticmethod
    def get_asset_col():
        return 'units'

    class Meta:
        list_serializer_class = MsaDictSerializer


class SFNewRentalsYYBigQuerySerializer(serializers.Serializer):

    @staticmethod
    def get_asset_col():
        return 'year_change_count'

    class Meta:
        list_serializer_class = DictSerializer


class SFNewRentalsYYMSABigQuerySerializer(serializers.Serializer):

    @staticmethod
    def get_asset_col():
        return 'year_change_count'

    class Meta:
        list_serializer_class = MsaDictSerializer


class SFNewRentalsYY2BigQuerySerializer(serializers.Serializer):

    @staticmethod
    def get_asset_col():
        return 'year_change_count2'

    class Meta:
        list_serializer_class = DictSerializer


class SFNewRentalsYY2MSABigQuerySerializer(serializers.Serializer):

    @staticmethod
    def get_asset_col():
        return 'year_change_count2'

    class Meta:
        list_serializer_class = MsaDictSerializer


class SFNewRentalsYYDifferntialBigQuerySerializer(serializers.Serializer):

    @staticmethod
    def get_asset_col():
        return 'year_chg_count'

    class Meta:
        list_serializer_class = DictSerializer


class SFNewRentalsYYDifferntialMSABigQuerySerializer(serializers.Serializer):

    @staticmethod
    def get_asset_col():
        return 'year_chg_count'

    class Meta:
        list_serializer_class = MsaDictSerializer


class SFFilterDataSerializer(serializers.Serializer):
    Portfolio_Year = serializers.ListField()
    MSA = serializers.ListField()
    Beds = serializers.ListField()

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data_keys = tuple(data.keys())
        for key in data_keys:
            if key == 'Beds':
                data[key] = natsorted(
                    list(map(int, set(filter(lambda item: item and item.isdigit(), data.pop(key, []))))))
            elif key == 'Portfolio_Year':
                data[key.replace('_', ' ')] = natsorted(list(map(int, set(data.pop(key, [])))))
            else:
                data[key.replace('_', ' ')] = natsorted(list(set(data.pop(key, []))))
        return data


# class SFNewRentalsRelativeAvgBigQuerySerializer(serializers.Serializer):
#
#     @staticmethod
#     def get_asset_col():
#         return 'apt_count'
#
#     @staticmethod
#     def get_rent_col():
#         return 'avg_rented_price'
#
#     @staticmethod
#     def get_base_avg_col():
#         return 'base_date_avg_rent'
#
#     class Meta:
#         list_serializer_class = RentalsRelativeAvgDictSerializer


class SFLeadDaysVsDaysRentBigQuerySerializer(serializers.Serializer):

    @staticmethod
    def get_asset_col():
        return 'median_days_to_rent'

    class Meta:
        list_serializer_class = DictSerializer


class SFNewRentalsRelativeAvgBigQuerySerializer(serializers.Serializer):

    @staticmethod
    def get_asset_col():
        return 'apt_count'

    class Meta:
        list_serializer_class = DictSerializer


class SFCSVBigQuerySerializer(serializers.Serializer):

    class Meta:
        list_serializer_class = DictSerializer


class SFCSVMSABigQuerySerializer(serializers.Serializer):

    class Meta:
        list_serializer_class = MsaDictSerializer


class SFAssetCountBigQuerySerializer(serializers.Serializer):

    @staticmethod
    def get_asset_col():
        return 'asset_count'

    class Meta:
        list_serializer_class = DictSerializer


class SFAssetCountMSABigQuerySerializer(serializers.Serializer):

    @staticmethod
    def get_asset_col():
        return 'asset_count'

    class Meta:
        list_serializer_class = MsaDictSerializer
