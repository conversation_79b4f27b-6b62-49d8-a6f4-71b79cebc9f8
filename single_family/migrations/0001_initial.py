# Generated by Django 3.2.2 on 2022-10-03 09:21

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SFPermissions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'permissions': (('Can view single_family dashboard', 'view_single_family_dashboard'),
                                ('Can access single_family api', 'access_single_family_api'),
                                ('Can download single_family csv', 'download_single_family_csv')),
                'managed': False,
            },
        ),
    ]
